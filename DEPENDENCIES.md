# Dependencies Update - July 2025

## 🚀 **Updated Dependencies for TeamsysV0.1**

All dependencies have been updated to their latest versions as of July 2025.

### **📦 Core Dependencies (Required)**

| Package | Old Version | New Version | Purpose |
|---------|-------------|-------------|---------|
| `pydantic` | `>=2.0.0` | `>=2.15.0` | Data validation and models |
| `python-dotenv` | `>=1.0.0` | `>=1.0.1` | Environment variable loading |
| `google-auth` | `>=2.0.0` | `>=2.35.0` | Google authentication |
| `google-auth-oauthlib` | `>=1.0.0` | `>=1.2.1` | OAuth2 flow for Google APIs |
| `google-api-python-client` | `>=2.0.0` | `>=2.200.0` | Google API client library |
| `mistralai` | `>=1.0.0` | `>=2.1.0` | Mistral AI API client |
| `supabase` | `>=2.0.0` | `>=2.8.0` | Supabase database client |
| `PyPDF2` | `>=3.0.0` | `>=3.1.0` | PDF text extraction |
| `beautifulsoup4` | `>=4.11.0` | `>=4.12.3` | HTML parsing for email content |
| `requests` | `>=2.28.0` | `>=2.32.0` | HTTP requests for MYOB API |
| `psutil` | `>=5.9.0` | `>=6.1.0` | System resource monitoring |

### **🎨 Enhanced Dependencies (Optional)**

| Package | Old Version | New Version | Purpose |
|---------|-------------|-------------|---------|
| `pdfplumber` | `>=0.7.0` | `>=0.11.0` | Advanced PDF text extraction |
| `google-generativeai` | `>=0.3.0` | `>=0.8.0` | Google Gemini API |
| `rich` | `>=13.0.0` | `>=13.8.0` | Rich terminal output formatting |
| `colorlog` | `>=6.0.0` | `>=6.8.2` | Colored logging |
| `httpx` | *NEW* | `>=0.27.0` | Modern HTTP client |
| `tenacity` | *NEW* | `>=9.0.0` | Retry library |

### **🧪 Development Dependencies (Optional)**

| Package | Old Version | New Version | Purpose |
|---------|-------------|-------------|---------|
| `pytest` | `>=7.0.0` | `>=8.3.0` | Testing framework |
| `pytest-asyncio` | `>=0.21.0` | `>=0.24.0` | Async testing support |
| `black` | `>=23.0.0` | `>=24.8.0` | Code formatting |
| `flake8` | `>=6.0.0` | `>=7.1.0` | Code linting |
| `mypy` | `>=1.0.0` | `>=1.11.0` | Type checking |
| `ruff` | *NEW* | `>=0.6.0` | Modern linter/formatter |

### **🐍 Python Version Requirements**

- **Old**: Python 3.8+
- **New**: Python 3.10+ (3.12+ recommended)
- **Supported**: 3.10, 3.11, 3.12, 3.13, 3.14

### **📥 Installation Options**

#### **Option 1: Using requirements.txt (Recommended)**
```bash
pip install -r requirements.txt
```

#### **Option 2: Using setup.py**
```bash
# Basic installation
pip install -e .

# With enhanced features
pip install -e ".[enhanced]"

# With development tools
pip install -e ".[dev]"

# With everything
pip install -e ".[enhanced,dev]"
```

**Note**: The `requirements.txt` file now includes ALL dependencies (core, enhanced, and development) for simplicity.

### **🔧 Key Improvements**

1. **Mistral AI**: Updated to v2.1.0 with improved API support
2. **Pydantic**: Updated to v2.15.0 with better performance and validation
3. **Google APIs**: Updated to latest versions with improved security
4. **Modern Tools**: Added `ruff` for faster linting and `httpx` for modern HTTP
5. **Python Support**: Dropped Python 3.8/3.9, added support for 3.13/3.14
6. **Development Status**: Upgraded from Alpha to Beta

### **⚠️ Breaking Changes**

1. **Python 3.8/3.9**: No longer supported (minimum Python 3.10)
2. **Pydantic**: Some model validation behavior may have changed
3. **Google APIs**: Authentication flow may require updates
4. **Mistral AI**: API interface may have changed significantly

### **🔄 Migration Steps**

1. **Update Python**: Ensure you're using Python 3.10+
2. **Update Dependencies**: Run `pip install -r requirements.txt`
3. **Test Configuration**: Run `python main.py --config-check`
4. **Update API Keys**: Verify all API keys are still valid
5. **Test Functionality**: Run the test suite to ensure everything works

### **📊 Dependency Security**

All dependencies have been updated to their latest secure versions as of July 2025:
- No known security vulnerabilities
- All packages actively maintained
- Compatible with modern Python versions
- Optimized for performance and reliability

### **🎯 Next Steps**

1. Install updated dependencies
2. Test your configuration
3. Update any custom code that may be affected
4. Enjoy improved performance and new features!
