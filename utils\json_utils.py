"""
JSON utility functions for parsing and cleaning JSON data.
"""

import json
import re
import logging
from typing import Any, Optional, Dict, Union

logger = logging.getLogger(__name__)


def clean_json_string(json_str: str) -> str:
    """
    Clean a JSON string by removing common formatting issues.
    
    Args:
        json_str: Raw JSON string that may have formatting issues
        
    Returns:
        Cleaned JSON string
    """
    if not json_str:
        return ""
    
    # Remove markdown code blocks
    json_str = re.sub(r'```json\s*', '', json_str)
    json_str = re.sub(r'```\s*$', '', json_str)
    
    # Remove leading/trailing whitespace
    json_str = json_str.strip()
    
    # Remove any text before the first { or [
    match = re.search(r'[{\[]', json_str)
    if match:
        json_str = json_str[match.start():]
    
    # Remove any text after the last } or ]
    # Find the last occurrence of } or ]
    last_brace = json_str.rfind('}')
    last_bracket = json_str.rfind(']')
    last_pos = max(last_brace, last_bracket)
    
    if last_pos != -1:
        json_str = json_str[:last_pos + 1]
    
    # Fix common JSON issues
    # Replace single quotes with double quotes (but be careful with apostrophes)
    json_str = re.sub(r"(?<!\\)'([^']*?)(?<!\\)'", r'"\1"', json_str)
    
    # Fix trailing commas
    json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)
    
    # Fix missing quotes around keys
    json_str = re.sub(r'(\w+):', r'"\1":', json_str)
    
    return json_str


def parse_json_safely(json_str: str, default: Any = None) -> Any:
    """
    Safely parse JSON string with error handling and cleaning.
    
    Args:
        json_str: JSON string to parse
        default: Default value to return if parsing fails
        
    Returns:
        Parsed JSON object or default value
    """
    if not json_str:
        return default
    
    # Try parsing as-is first
    try:
        return json.loads(json_str)
    except json.JSONDecodeError:
        pass
    
    # Try with cleaning
    try:
        cleaned = clean_json_string(json_str)
        return json.loads(cleaned)
    except json.JSONDecodeError as e:
        logger.warning(f"JSON parsing failed even after cleaning: {e}")
        logger.debug(f"Failed JSON string: {json_str[:200]}...")
        return default


def extract_json_from_text(text: str) -> Optional[Dict[str, Any]]:
    """
    Extract JSON object from text that may contain other content.
    
    Args:
        text: Text that may contain JSON
        
    Returns:
        Extracted JSON object or None
    """
    if not text:
        return None
    
    # Look for JSON-like patterns
    patterns = [
        r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}',  # Simple nested objects
        r'\[[^\[\]]*(?:\[[^\[\]]*\][^\[\]]*)*\]',  # Arrays
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, text, re.DOTALL)
        for match in matches:
            result = parse_json_safely(match)
            if result is not None:
                return result
    
    return None


def validate_json_structure(data: Any, required_keys: list = None) -> bool:
    """
    Validate that JSON data has the expected structure.
    
    Args:
        data: Parsed JSON data
        required_keys: List of required keys for dict objects
        
    Returns:
        True if structure is valid
    """
    if data is None:
        return False
    
    if required_keys and isinstance(data, dict):
        return all(key in data for key in required_keys)
    
    return True


def merge_json_objects(obj1: Dict[str, Any], obj2: Dict[str, Any], 
                      prefer_obj2: bool = True) -> Dict[str, Any]:
    """
    Merge two JSON objects.
    
    Args:
        obj1: First JSON object
        obj2: Second JSON object  
        prefer_obj2: If True, obj2 values override obj1 values
        
    Returns:
        Merged JSON object
    """
    if not isinstance(obj1, dict) or not isinstance(obj2, dict):
        return obj2 if prefer_obj2 else obj1
    
    result = obj1.copy()
    
    for key, value in obj2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            # Recursively merge nested objects
            result[key] = merge_json_objects(result[key], value, prefer_obj2)
        else:
            # Override or add the value
            if prefer_obj2 or key not in result:
                result[key] = value
    
    return result


def flatten_json(data: Dict[str, Any], separator: str = '.') -> Dict[str, Any]:
    """
    Flatten a nested JSON object.
    
    Args:
        data: Nested JSON object
        separator: Separator for flattened keys
        
    Returns:
        Flattened JSON object
    """
    def _flatten(obj: Any, parent_key: str = '') -> Dict[str, Any]:
        items = []
        
        if isinstance(obj, dict):
            for key, value in obj.items():
                new_key = f"{parent_key}{separator}{key}" if parent_key else key
                items.extend(_flatten(value, new_key).items())
        elif isinstance(obj, list):
            for i, value in enumerate(obj):
                new_key = f"{parent_key}{separator}{i}" if parent_key else str(i)
                items.extend(_flatten(value, new_key).items())
        else:
            return {parent_key: obj}
        
        return dict(items)
    
    return _flatten(data)


def unflatten_json(data: Dict[str, Any], separator: str = '.') -> Dict[str, Any]:
    """
    Unflatten a flattened JSON object.
    
    Args:
        data: Flattened JSON object
        separator: Separator used in flattened keys
        
    Returns:
        Nested JSON object
    """
    result = {}
    
    for key, value in data.items():
        keys = key.split(separator)
        current = result
        
        for k in keys[:-1]:
            if k not in current:
                # Check if next key is numeric (for arrays)
                next_key = keys[keys.index(k) + 1]
                current[k] = [] if next_key.isdigit() else {}
            current = current[k]
        
        final_key = keys[-1]
        if final_key.isdigit() and isinstance(current, list):
            # Extend list if necessary
            index = int(final_key)
            while len(current) <= index:
                current.append(None)
            current[index] = value
        else:
            current[final_key] = value
    
    return result


def sanitize_json_for_logging(data: Any, max_length: int = 1000) -> str:
    """
    Sanitize JSON data for safe logging.
    
    Args:
        data: JSON data to sanitize
        max_length: Maximum length of output string
        
    Returns:
        Sanitized JSON string
    """
    try:
        # Convert to JSON string
        json_str = json.dumps(data, indent=2, default=str)
        
        # Truncate if too long
        if len(json_str) > max_length:
            json_str = json_str[:max_length] + "... (truncated)"
        
        # Remove sensitive information
        sensitive_patterns = [
            (r'"password":\s*"[^"]*"', '"password": "***"'),
            (r'"api_key":\s*"[^"]*"', '"api_key": "***"'),
            (r'"token":\s*"[^"]*"', '"token": "***"'),
            (r'"secret":\s*"[^"]*"', '"secret": "***"'),
        ]
        
        for pattern, replacement in sensitive_patterns:
            json_str = re.sub(pattern, replacement, json_str, flags=re.IGNORECASE)
        
        return json_str
        
    except Exception as e:
        return f"<Error serializing data for logging: {e}>"
