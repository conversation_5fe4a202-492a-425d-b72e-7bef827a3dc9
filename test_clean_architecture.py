#!/usr/bin/env python3
"""
Comprehensive test script for the clean TeamsysV0.1 architecture
"""
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_core_imports():
    """Test core module imports."""
    print("📦 Testing core imports...")
    
    try:
        from core.config import config
        from core.models import EmailData, ExtractedOrder, ProcessedOrder
        from core.exceptions import TeamsysException, EmailProcessingError
        from core.constants import APP_NAME, DEFAULT_LABELS
        
        print(f"  ✅ Config loaded: {config.APP_NAME}")
        print(f"  ✅ Models available: EmailData, ExtractedOrder, ProcessedOrder")
        print(f"  ✅ Exceptions available: TeamsysException, EmailProcessingError")
        print(f"  ✅ Constants loaded: {APP_NAME}, {len(DEFAULT_LABELS)} default labels")
        return True
        
    except ImportError as e:
        print(f"  ❌ Core import failed: {e}")
        return False

def test_service_imports():
    """Test service module imports."""
    print("\n🔧 Testing service imports...")
    
    try:
        from services.email import GmailService
        from services.erp import MYOBService
        from services.database import SupabaseService
        from services.llm import LLMService
        from services.monitoring import HealthMonitor
        
        print("  ✅ Email service: GmailService")
        print("  ✅ ERP service: MYOBService")
        print("  ✅ Database service: SupabaseService")
        print("  ✅ LLM service: LLMService")
        print("  ✅ Monitoring service: HealthMonitor")
        return True
        
    except ImportError as e:
        print(f"  ❌ Service import failed: {e}")
        return False

def test_processor_imports():
    """Test processor module imports."""
    print("\n⚙️ Testing processor imports...")
    
    try:
        from processors.email_processor import EmailProcessor
        from processors.order_processor import OrderProcessor
        from processors.report_processor import ReportProcessor
        
        print("  ✅ Email processor: EmailProcessor")
        print("  ✅ Order processor: OrderProcessor")
        print("  ✅ Report processor: ReportProcessor")
        return True
        
    except ImportError as e:
        print(f"  ❌ Processor import failed: {e}")
        return False

def test_utility_imports():
    """Test utility module imports."""
    print("\n🛠️ Testing utility imports...")
    
    try:
        from utils.pdf import extract_text_from_pdf
        from utils.json_utils import clean_json_string, parse_json_safely
        from utils.validation import validate_email_data, validate_order_data
        
        print("  ✅ PDF utilities: extract_text_from_pdf")
        print("  ✅ JSON utilities: clean_json_string, parse_json_safely")
        print("  ✅ Validation utilities: validate_email_data, validate_order_data")
        return True
        
    except ImportError as e:
        print(f"  ❌ Utility import failed: {e}")
        return False

def test_cli_imports():
    """Test CLI module imports."""
    print("\n💻 Testing CLI imports...")
    
    try:
        from cli.main import main, cli_main
        from cli.commands import CLICommands
        
        print("  ✅ CLI main: main, cli_main")
        print("  ✅ CLI commands: CLICommands")
        return True
        
    except ImportError as e:
        print(f"  ❌ CLI import failed: {e}")
        return False

def test_package_imports():
    """Test top-level package imports."""
    print("\n📋 Testing package-level imports...")
    
    try:
        # Test root package imports
        from core.config import config
        from core.models import EmailData
        from services.email import GmailService

        print(f"  ✅ Config loaded: {config.APP_NAME}")
        print(f"  ✅ Models available: EmailData")
        print(f"  ✅ Services available: GmailService")

        # Test convenience imports from root __init__.py
        import __init__ as root_package
        print("  ✅ Convenience imports work")
        return True
        
    except ImportError as e:
        print(f"  ❌ Package import failed: {e}")
        return False

def test_model_creation():
    """Test creating and validating models."""
    print("\n📊 Testing model creation...")
    
    try:
        from core.models import EmailData, CustomerDetails, OrderLine, ExtractedOrder
        
        # Test EmailData creation
        email = EmailData(
            gmail_id="test123",
            subject="Test Email",
            sender="<EMAIL>",
            body="Test body content"
        )
        print(f"  ✅ EmailData created: {email.subject}")
        
        # Test CustomerDetails creation
        customer = CustomerDetails(customer_name="Test Customer")
        print(f"  ✅ CustomerDetails created: {customer.customer_name}")
        
        # Test OrderLine creation
        line = OrderLine(stock_code="ABC123", quantity=5.0)
        print(f"  ✅ OrderLine created: {line.stock_code} x {line.quantity}")
        
        # Test ExtractedOrder creation
        order = ExtractedOrder(
            customer_details=customer,
            order_lines=[line]
        )
        print(f"  ✅ ExtractedOrder created with {len(order.order_lines)} lines")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Model creation failed: {e}")
        return False

def test_validation():
    """Test validation utilities."""
    print("\n✅ Testing validation...")
    
    try:
        from utils.validation import validate_email_data, validate_order_data
        from core.models import EmailData, CustomerDetails, OrderLine, ExtractedOrder
        
        # Test email validation
        email = EmailData(
            gmail_id="test123",
            subject="Test Email",
            sender="<EMAIL>",
            body="Test body"
        )
        
        is_valid, errors = validate_email_data(email)
        print(f"  ✅ Email validation: {is_valid} (errors: {len(errors)})")
        
        # Test order validation
        customer = CustomerDetails(customer_name="Test Customer")
        line = OrderLine(stock_code="ABC123", quantity=5.0)
        order = ExtractedOrder(customer_details=customer, order_lines=[line])
        
        is_valid, errors = validate_order_data(order)
        print(f"  ✅ Order validation: {is_valid} (errors: {len(errors)})")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Validation test failed: {e}")
        return False

def test_configuration():
    """Test configuration loading."""
    print("\n⚙️ Testing configuration...")
    
    try:
        from core.config import config
        
        print(f"  ✅ App name: {config.APP_NAME}")
        print(f"  ✅ App version: {config.APP_VERSION}")
        print(f"  ✅ Gmail scopes: {len(config.GMAIL_SCOPES)} configured")
        print(f"  ✅ Labels to process: {len(config.GMAIL_LABELS_TO_PROCESS)}")
        print(f"  ✅ Max Gmail results: {config.MAX_GMAIL_RESULTS}")
        
        # Test validation
        errors = config.validate()
        print(f"  ✅ Configuration validation: {len(errors)} errors found")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Configuration test failed: {e}")
        return False

def test_json_utilities():
    """Test JSON utility functions."""
    print("\n🔧 Testing JSON utilities...")
    
    try:
        from utils.json_utils import clean_json_string, parse_json_safely
        
        # Test JSON cleaning
        dirty_json = '```json\n{"test": "value"}\n```'
        clean_json = clean_json_string(dirty_json)
        print(f"  ✅ JSON cleaning works")
        
        # Test JSON parsing
        result = parse_json_safely('{"test": "value"}')
        print(f"  ✅ JSON parsing: {result}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ JSON utilities test failed: {e}")
        return False

def test_directory_structure():
    """Test that all expected directories and files exist."""
    print("\n📁 Testing directory structure...")
    
    expected_files = [
        "__init__.py",
        "core/__init__.py",
        "core/config.py",
        "core/models.py",
        "core/exceptions.py",
        "core/constants.py",
        "services/__init__.py",
        "services/email.py",
        "services/erp.py",
        "services/database.py",
        "services/llm.py",
        "services/monitoring.py",
        "processors/__init__.py",
        "processors/email_processor.py",
        "processors/order_processor.py",
        "processors/report_processor.py",
        "utils/__init__.py",
        "utils/pdf.py",
        "utils/json_utils.py",
        "utils/validation.py",
        "cli/__init__.py",
        "cli/main.py",
        "cli/commands.py",
        "main.py",
        "setup.py"
    ]
    
    missing_files = []
    for file_path in expected_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"  ❌ Missing files: {missing_files}")
        return False
    else:
        print(f"  ✅ All {len(expected_files)} expected files exist")
        return True

def main():
    """Run all tests."""
    print("🧪 Testing TeamsysV0.1 Clean Architecture")
    print("=" * 60)
    
    tests = [
        test_directory_structure,
        test_core_imports,
        test_service_imports,
        test_processor_imports,
        test_utility_imports,
        test_cli_imports,
        test_package_imports,
        test_model_creation,
        test_validation,
        test_configuration,
        test_json_utilities,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"  ❌ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Clean architecture is working perfectly.")
        return True
    else:
        print("❌ Some tests failed. Check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
