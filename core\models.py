"""
Data models for TeamsysV0.1
Centralized data models using Pydantic for validation.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum


class EmailPriority(str, Enum):
    """Email priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class OrderStatus(int, Enum):
    """Order status values."""
    DRAFT = 0
    CONFIRMED = 1
    CANCELLED = 2


class EmailAttachment(BaseModel):
    """Email attachment model."""
    filename: str
    content_type: Optional[str] = None
    size_bytes: Optional[int] = None
    data: Optional[bytes] = None
    processed: bool = False
    extracted_text: Optional[str] = None


class EmailData(BaseModel):
    """Email data model."""
    id: Optional[str] = None
    gmail_id: str
    subject: str
    sender: str
    recipient: Optional[str] = None
    body: str
    html_body: Optional[str] = None
    thread_id: Optional[str] = None
    label_ids: List[str] = []
    received_date: Optional[datetime] = None
    processed_date: Optional[datetime] = None
    has_attachments: bool = False
    attachments: List[EmailAttachment] = []
    priority: EmailPriority = EmailPriority.NORMAL


class CustomerDetails(BaseModel):
    """Customer details model."""
    debtor_id: Optional[int] = None
    customer_name: str
    email: Optional[str] = None
    phone: Optional[str] = None
    
    class Config:
        str_strip_whitespace = True


class DeliveryAddress(BaseModel):
    """Delivery address model."""
    line1: Optional[str] = None
    line2: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    postal_code: Optional[str] = None
    country: str = "Australia"


class OrderLine(BaseModel):
    """Order line item model."""
    stock_code: str
    description: Optional[str] = None
    quantity: float = Field(gt=0)
    unit_price: Optional[float] = Field(ge=0, default=None)
    line_total: Optional[float] = Field(ge=0, default=None)
    unit_of_measure: Optional[str] = "EA"
    
    @validator('quantity')
    def validate_quantity(cls, v):
        if v <= 0:
            raise ValueError('Quantity must be greater than 0')
        return v


class ExtractedOrder(BaseModel):
    """Extracted order data model."""
    customer_details: CustomerDetails
    order_lines: List[OrderLine]
    purchase_order_number: Optional[str] = None
    delivery_address: Optional[DeliveryAddress] = None
    special_instructions: Optional[str] = None
    total_amount: Optional[float] = Field(ge=0, default=None)
    currency: str = "AUD"
    order_date: Optional[datetime] = None
    
    @validator('order_lines')
    def validate_order_lines(cls, v):
        if not v:
            raise ValueError('Order must have at least one line item')
        return v


class MYOBPayload(BaseModel):
    """MYOB API payload model."""
    debtorid: int
    customerordernumber: Optional[str] = None
    status: int = OrderStatus.DRAFT
    defaultlocationid: int = 1
    lines: List[Dict[str, Any]]
    deliveryaddress: Optional[Dict[str, Optional[str]]] = None
    extrafields: Optional[List[Dict[str, str]]] = None
    
    @validator('lines')
    def validate_lines(cls, v):
        if not v:
            raise ValueError('MYOB payload must have at least one line')
        for line in v:
            if 'stockcode' not in line or 'orderquantity' not in line:
                raise ValueError('Each line must have stockcode and orderquantity')
        return v


class ProcessedOrder(BaseModel):
    """Final processed order model."""
    email_id: str
    email_subject: str
    email_data: EmailData
    extracted_order: Optional[ExtractedOrder] = None
    myob_payload: Optional[MYOBPayload] = None
    markdown_summary: Optional[str] = None
    processing_status: str = "pending"
    error_message: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    
    class Config:
        arbitrary_types_allowed = True


class HealthStatus(BaseModel):
    """System health status model."""
    service_name: str
    status: str  # "healthy", "degraded", "unhealthy"
    last_check: datetime
    response_time_ms: Optional[float] = None
    error_message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
