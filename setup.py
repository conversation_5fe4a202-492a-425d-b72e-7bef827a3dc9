from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="teamsys",
    version="0.1.0",
    description="Unified Email & MYOB Management System",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="Team Systems",
    packages=find_packages(),
    entry_points={
        'console_scripts': [
            'teamsys=cli.main:cli_main',
        ],
    },
    install_requires=[
        # Core dependencies
        "pydantic>=2.15.0",
        "python-dotenv>=1.0.1",

        # Gmail API
        "google-auth>=2.35.0",
        "google-auth-oauthlib>=1.2.1",
        "google-api-python-client>=2.200.0",

        # LLM services
        "mistralai>=2.1.0",

        # Database
        "supabase>=2.8.0",

        # PDF processing
        "PyPDF2>=3.1.0",

        # Email processing
        "beautifulsoup4>=4.12.3",

        # Utilities
        "requests>=2.32.0",
        "psutil>=6.1.0",
    ],
    extras_require={
        "dev": [
            "pytest>=8.3.0",
            "pytest-asyncio>=0.24.0",
            "black>=24.8.0",
            "flake8>=7.1.0",
            "mypy>=1.11.0",
            "ruff>=0.6.0",  # Modern linter/formatter
        ],
        "enhanced": [
            "pdfplumber>=0.11.0",
            "google-generativeai>=0.8.0",
            "rich>=13.8.0",
            "colorlog>=6.8.2",
            "httpx>=0.27.0",  # Modern HTTP client
            "tenacity>=9.0.0",  # Retry library
        ]
    },
    python_requires=">=3.10",
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Programming Language :: Python :: 3.13",
        "Programming Language :: Python :: 3.14",
    ],
    keywords="email automation myob erp order processing",
    project_urls={
        "Bug Reports": "https://github.com/slothmc-ctrl/TeamsysV0.1/issues",
        "Source": "https://github.com/slothmc-ctrl/TeamsysV0.1",
    },
)
