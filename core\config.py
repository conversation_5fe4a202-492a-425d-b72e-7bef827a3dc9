"""
Configuration module for TeamsysV0.1
Centralized configuration management with environment variables.
"""

import os
import base64
import logging
from pathlib import Path
from typing import List, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class Config:
    """Centralized configuration class."""
    
    # Application settings
    APP_NAME = "TeamsysV0.1"
    APP_VERSION = "0.1.0"
    DEBUG = os.getenv("DEBUG", "false").lower() == "true"
    
    # Gmail API Configuration
    GMAIL_SCOPES = [
        'https://www.googleapis.com/auth/gmail.readonly',
        'https://www.googleapis.com/auth/gmail.send',
        'https://www.googleapis.com/auth/gmail.modify'
    ]
    GMAIL_CREDENTIALS_FILE = os.getenv('GMAIL_CREDENTIALS_FILE', 'credentials.json')
    GMAIL_TOKEN_FILE = os.getenv('GMAIL_TOKEN_FILE', 'token.pickle')
    GMAIL_PORT = int(os.getenv('GMAIL_PORT', '8080'))
    GMAIL_LABELS_TO_PROCESS = [
        label.strip() for label in os.getenv('GMAIL_LABELS_TO_PROCESS', 'Brady,RSEA,Woolworths').split(',')
        if label.strip()
    ]
    MAX_GMAIL_RESULTS = int(os.getenv('MAX_GMAIL_RESULTS', '40'))
    DEFAULT_EMAIL_TIME_FILTER_HOURS = int(os.getenv('DEFAULT_EMAIL_TIME_FILTER_HOURS', '24'))

    # LLM Configuration
    MISTRAL_API_KEY = os.getenv("MISTRAL_API_KEY")
    MISTRAL_MODEL = os.getenv("MISTRAL_MODEL", "mistral-medium-latest")
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
    GEMINI_MODEL = os.getenv("GEMINI_MODEL", "gemini-2.5-flash-preview-05-20")
    
    # MYOB API Configuration
    EXO_IP = os.getenv("EXO_IP")
    EXO_PORT = os.getenv("EXO_PORT")
    MYOB_USER = os.getenv("MYOB_USER")
    MYOB_PASSWORD = os.getenv("MYOB_PASSWORD")
    MYOB_API_KEY = os.getenv("MYOB_API_KEY")
    MYOB_TOKEN = os.getenv("MYOB_TOKEN")
    
    # Supabase Configuration
    SUPABASE_URL = os.getenv("SUPABASE_URL")
    SUPABASE_KEY = os.getenv("SUPABASE_KEY")
    
    # File paths
    OUTPUT_DIR = Path(os.getenv("OUTPUT_DIR", "."))
    MARKDOWN_DIR = OUTPUT_DIR / "markdown"
    MYOB_DIR = OUTPUT_DIR / "myob"
    LOGS_DIR = OUTPUT_DIR / "logs"
    
    @property
    def MYOB_BASE_URL(self) -> Optional[str]:
        """Get MYOB base URL."""
        if self.EXO_IP and self.EXO_PORT:
            return f"http://{self.EXO_IP}:{self.EXO_PORT}"
        return None
    
    @property
    def MYOB_HEADERS(self) -> dict:
        """Get MYOB API headers."""
        if not all([self.MYOB_USER, self.MYOB_PASSWORD, self.MYOB_API_KEY, self.MYOB_TOKEN]):
            return {}
        
        auth_string = f"{self.MYOB_USER}:{self.MYOB_PASSWORD}"
        base64_auth = base64.b64encode(auth_string.encode()).decode()
        
        return {
            "Authorization": f"Basic {base64_auth}",
            "Accept": "application/json",
            "Content-Type": "application/json",
            "x-myobapi-key": self.MYOB_API_KEY,
            "x-myobapi-exoToken": self.MYOB_TOKEN
        }
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of errors."""
        errors = []
        
        # Check Gmail credentials
        if not os.path.exists(self.GMAIL_CREDENTIALS_FILE):
            errors.append(f"Gmail credentials file not found: {self.GMAIL_CREDENTIALS_FILE}")
        
        # Check LLM API keys
        if not self.MISTRAL_API_KEY and not self.GEMINI_API_KEY:
            errors.append("No LLM API key configured (MISTRAL_API_KEY or GEMINI_API_KEY)")
        
        # Check MYOB configuration
        myob_vars = [self.EXO_IP, self.EXO_PORT, self.MYOB_USER, self.MYOB_PASSWORD, 
                     self.MYOB_API_KEY, self.MYOB_TOKEN]
        if any(myob_vars) and not all(myob_vars):
            errors.append("Incomplete MYOB configuration - all MYOB variables must be set")
        
        # Check Supabase configuration
        if not self.SUPABASE_URL or not self.SUPABASE_KEY:
            errors.append("Supabase configuration missing (SUPABASE_URL and SUPABASE_KEY required)")
        
        return errors
    
    def ensure_directories(self):
        """Ensure output directories exist."""
        for directory in [self.MARKDOWN_DIR, self.MYOB_DIR, self.LOGS_DIR]:
            directory.mkdir(parents=True, exist_ok=True)

# Global configuration instance
config = Config()
