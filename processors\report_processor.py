"""
Report processor for generating summaries and analytics.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from pathlib import Path

from core.config import config
from core.models import ProcessedOrder, EmailData
from services.database import SupabaseService

logger = logging.getLogger(__name__)


class ReportProcessor:
    """Report generation and analytics processor."""
    
    def __init__(self):
        """Initialize report processor."""
        self.db_service = SupabaseService()
        
        logger.info("Report processor initialized")
    
    def generate_daily_summary(self, date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        Generate daily processing summary.
        
        Args:
            date: Date to generate summary for (defaults to today)
            
        Returns:
            Daily summary data
        """
        if not date:
            date = datetime.now()
        
        try:
            # Get processing stats for the day
            stats = self.db_service.get_processing_stats(days=1)
            
            summary = {
                'date': date.strftime('%Y-%m-%d'),
                'total_emails': stats.get('total_emails', 0),
                'processed_emails': stats.get('processed_emails', 0),
                'total_orders': stats.get('total_orders', 0),
                'successful_orders': stats.get('successful_orders', 0),
                'processing_rate': stats.get('processing_rate', 0),
                'success_rate': stats.get('success_rate', 0),
                'generated_at': datetime.now().isoformat()
            }
            
            logger.info(f"Generated daily summary for {date.strftime('%Y-%m-%d')}")
            return summary
            
        except Exception as e:
            logger.error(f"Failed to generate daily summary: {e}")
            return {}
    
    def generate_weekly_report(self, weeks_back: int = 0) -> Dict[str, Any]:
        """
        Generate weekly processing report.
        
        Args:
            weeks_back: Number of weeks back from current week
            
        Returns:
            Weekly report data
        """
        try:
            # Calculate week start and end
            today = datetime.now()
            week_start = today - timedelta(days=today.weekday() + (weeks_back * 7))
            week_end = week_start + timedelta(days=6)
            
            # Get stats for the week
            stats = self.db_service.get_processing_stats(days=7)
            
            report = {
                'week_start': week_start.strftime('%Y-%m-%d'),
                'week_end': week_end.strftime('%Y-%m-%d'),
                'summary': stats,
                'daily_breakdown': [],
                'top_customers': [],
                'error_analysis': {},
                'generated_at': datetime.now().isoformat()
            }
            
            # Generate daily breakdown for the week
            for i in range(7):
                day = week_start + timedelta(days=i)
                daily_stats = self.generate_daily_summary(day)
                report['daily_breakdown'].append(daily_stats)
            
            logger.info(f"Generated weekly report for {week_start.strftime('%Y-%m-%d')} to {week_end.strftime('%Y-%m-%d')}")
            return report
            
        except Exception as e:
            logger.error(f"Failed to generate weekly report: {e}")
            return {}
    
    def generate_customer_report(self, customer_name: Optional[str] = None, 
                                days: int = 30) -> Dict[str, Any]:
        """
        Generate customer-specific processing report.
        
        Args:
            customer_name: Specific customer name (None for all customers)
            days: Number of days to analyze
            
        Returns:
            Customer report data
        """
        try:
            # This would require additional database queries
            # For now, return a placeholder structure
            report = {
                'customer_name': customer_name or 'All Customers',
                'period_days': days,
                'total_orders': 0,
                'total_value': 0.0,
                'average_order_value': 0.0,
                'order_frequency': 0.0,
                'top_products': [],
                'order_history': [],
                'generated_at': datetime.now().isoformat()
            }
            
            logger.info(f"Generated customer report for {customer_name or 'all customers'}")
            return report
            
        except Exception as e:
            logger.error(f"Failed to generate customer report: {e}")
            return {}
    
    def generate_error_report(self, days: int = 7) -> Dict[str, Any]:
        """
        Generate error analysis report.
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Error report data
        """
        try:
            # Get failed emails from database
            # This would require additional database queries
            report = {
                'period_days': days,
                'total_errors': 0,
                'error_categories': {},
                'common_errors': [],
                'error_trends': [],
                'recommendations': [],
                'generated_at': datetime.now().isoformat()
            }
            
            logger.info(f"Generated error report for last {days} days")
            return report
            
        except Exception as e:
            logger.error(f"Failed to generate error report: {e}")
            return {}
    
    def save_report_to_file(self, report_data: Dict[str, Any], 
                           report_type: str, filename: Optional[str] = None) -> Optional[str]:
        """
        Save report data to file.
        
        Args:
            report_data: Report data to save
            report_type: Type of report (daily, weekly, customer, error)
            filename: Custom filename (optional)
            
        Returns:
            Path to saved file or None if failed
        """
        try:
            # Ensure reports directory exists
            reports_dir = config.OUTPUT_DIR / "reports"
            reports_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate filename if not provided
            if not filename:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"{report_type}_report_{timestamp}.json"
            
            file_path = reports_dir / filename
            
            # Save as JSON
            import json
            with open(file_path, 'w') as f:
                json.dump(report_data, f, indent=2, default=str)
            
            logger.info(f"Saved {report_type} report to: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"Failed to save report to file: {e}")
            return None
    
    def generate_markdown_summary(self, processed_order: ProcessedOrder) -> str:
        """
        Generate markdown summary for a processed order.
        
        Args:
            processed_order: Processed order data
            
        Returns:
            Markdown formatted summary
        """
        try:
            lines = []
            
            # Header
            lines.append(f"# Order Summary: {processed_order.email_subject}")
            lines.append(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            lines.append("")
            
            # Email information
            lines.append("## Email Information")
            lines.append(f"- **From:** {processed_order.email_data.sender}")
            lines.append(f"- **Subject:** {processed_order.email_subject}")
            lines.append(f"- **Received:** {processed_order.email_data.received_date or 'Unknown'}")
            lines.append(f"- **Processing Status:** {processed_order.processing_status}")
            lines.append("")
            
            # Order details
            if processed_order.extracted_order:
                order = processed_order.extracted_order
                lines.append("## Order Details")
                lines.append(f"- **Customer:** {order.customer_details.customer_name}")
                
                if order.purchase_order_number:
                    lines.append(f"- **PO Number:** {order.purchase_order_number}")
                
                if order.total_amount:
                    lines.append(f"- **Total Amount:** {order.currency} {order.total_amount}")
                
                lines.append("")
                
                # Order lines
                lines.append("### Order Lines")
                for i, line in enumerate(order.order_lines, 1):
                    lines.append(f"{i}. **{line.stock_code}** - Qty: {line.quantity}")
                    if line.description:
                        lines.append(f"   - Description: {line.description}")
                    if line.unit_price:
                        lines.append(f"   - Unit Price: ${line.unit_price}")
                
                lines.append("")
            
            # MYOB information
            if processed_order.myob_payload:
                lines.append("## MYOB Integration")
                lines.append(f"- **Debtor ID:** {processed_order.myob_payload.debtorid}")
                lines.append(f"- **Status:** {processed_order.myob_payload.status}")
                lines.append(f"- **Lines:** {len(processed_order.myob_payload.lines)}")
                lines.append("")
            
            # Error information
            if processed_order.error_message:
                lines.append("## Processing Errors")
                lines.append(f"```")
                lines.append(processed_order.error_message)
                lines.append(f"```")
                lines.append("")
            
            summary = "\n".join(lines)
            logger.info(f"Generated markdown summary for order: {processed_order.email_subject}")
            return summary
            
        except Exception as e:
            logger.error(f"Failed to generate markdown summary: {e}")
            return f"Error generating summary: {e}"
    
    def save_markdown_summary(self, processed_order: ProcessedOrder) -> Optional[str]:
        """
        Save markdown summary to file.
        
        Args:
            processed_order: Processed order data
            
        Returns:
            Path to saved file or None if failed
        """
        try:
            # Generate summary
            summary = self.generate_markdown_summary(processed_order)
            
            # Ensure markdown directory exists
            config.ensure_directories()
            
            # Generate filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            safe_subject = "".join(c for c in processed_order.email_subject if c.isalnum() or c in (' ', '-', '_')).rstrip()[:50]
            filename = f"{timestamp}_{safe_subject}.md"
            
            file_path = config.MARKDOWN_DIR / filename
            
            # Save file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(summary)
            
            logger.info(f"Saved markdown summary to: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"Failed to save markdown summary: {e}")
            return None
