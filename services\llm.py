"""
LLM service for AI-powered text processing.
"""

import json
import logging
import re
from typing import Dict, Any, Optional, List
from datetime import datetime

from core.config import config
from core.models import ExtractedOrder, EmailData
from core.exceptions import LLMServiceError, ConfigurationError

logger = logging.getLogger(__name__)


class LLMService:
    """LLM service for AI processing."""
    
    def __init__(self):
        """Initialize LLM service."""
        self.mistral_api_key = config.MISTRAL_API_KEY
        self.gemini_api_key = config.GEMINI_API_KEY
        
        if not self.mistral_api_key and not self.gemini_api_key:
            raise ConfigurationError("No LLM API key configured")
        
        self.preferred_model = "mistral" if self.mistral_api_key else "gemini"
        logger.info(f"LLM service initialized with {self.preferred_model}")
    
    def analyze_email(self, email_data: EmailData) -> Dict[str, Any]:
        """Analyze email to determine if it contains order information."""
        try:
            # Combine email content
            content = f"Subject: {email_data.subject}\n\nBody:\n{email_data.body}"
            
            # Add attachment info
            if email_data.attachments:
                content += f"\n\nAttachments: {', '.join([att.filename for att in email_data.attachments])}"
            
            prompt = self._build_analysis_prompt(content)
            
            # For now, return a simple analysis
            # TODO: Implement actual LLM API calls
            analysis = self._simple_analysis(content)
            
            logger.info(f"Analyzed email: {email_data.subject}")
            return analysis
            
        except Exception as e:
            logger.error(f"Email analysis failed: {e}")
            raise LLMServiceError(f"Email analysis failed: {e}")
    
    def extract_order_data(self, email_data: EmailData, pdf_text: Optional[str] = None) -> Optional[ExtractedOrder]:
        """Extract structured order data from email and PDF content."""
        try:
            # Combine all available text
            content = f"Subject: {email_data.subject}\n\nEmail Body:\n{email_data.body}"
            
            if pdf_text:
                content += f"\n\nPDF Content:\n{pdf_text}"
            
            prompt = self._build_extraction_prompt(content)
            
            # For now, return a simple extraction
            # TODO: Implement actual LLM API calls
            extracted_data = self._simple_extraction(content, email_data)
            
            if extracted_data:
                logger.info(f"Extracted order data from: {email_data.subject}")
            
            return extracted_data
            
        except Exception as e:
            logger.error(f"Order extraction failed: {e}")
            raise LLMServiceError(f"Order extraction failed: {e}")
    
    def generate_summary(self, email_data: EmailData, extracted_order: Optional[ExtractedOrder] = None) -> str:
        """Generate markdown summary of processed email."""
        try:
            summary_parts = []
            
            # Email header
            summary_parts.append(f"# Email Summary: {email_data.subject}")
            summary_parts.append(f"**From:** {email_data.sender}")
            summary_parts.append(f"**Date:** {email_data.received_date or 'Unknown'}")
            summary_parts.append("")
            
            # Email content
            summary_parts.append("## Email Content")
            summary_parts.append(email_data.body[:500] + "..." if len(email_data.body) > 500 else email_data.body)
            summary_parts.append("")
            
            # Attachments
            if email_data.attachments:
                summary_parts.append("## Attachments")
                for att in email_data.attachments:
                    summary_parts.append(f"- {att.filename} ({att.content_type})")
                summary_parts.append("")
            
            # Extracted order data
            if extracted_order:
                summary_parts.append("## Extracted Order Data")
                summary_parts.append(f"**Customer:** {extracted_order.customer_details.customer_name}")
                if extracted_order.purchase_order_number:
                    summary_parts.append(f"**PO Number:** {extracted_order.purchase_order_number}")
                
                summary_parts.append("### Order Lines")
                for line in extracted_order.order_lines:
                    summary_parts.append(f"- {line.stock_code}: {line.quantity} {line.unit_of_measure or 'EA'}")
                
                if extracted_order.total_amount:
                    summary_parts.append(f"**Total:** {extracted_order.currency} {extracted_order.total_amount}")
            
            summary = "\n".join(summary_parts)
            logger.info(f"Generated summary for: {email_data.subject}")
            return summary
            
        except Exception as e:
            logger.error(f"Summary generation failed: {e}")
            return f"Error generating summary: {e}"
    
    def _build_analysis_prompt(self, content: str) -> str:
        """Build prompt for email analysis."""
        return f"""
Analyze this email content and determine if it contains order information.

Email Content:
{content}

Return a JSON object with:
- is_order: boolean (true if this contains order information)
- confidence: float between 0 and 1
- order_type: string (purchase_order, sales_order, invoice, etc.)
- summary: brief description of the content
- key_indicators: list of phrases that indicate this is an order
"""
    
    def _build_extraction_prompt(self, content: str) -> str:
        """Build prompt for order data extraction."""
        return f"""
Extract structured order data from this content.

Content:
{content}

Return a JSON object with:
- customer_name: string
- purchase_order_number: string (if found)
- order_lines: array of objects with stock_code, quantity, description
- delivery_address: object with address components
- total_amount: number (if found)
- currency: string (default "AUD")
- special_instructions: string (if any)
"""
    
    def _simple_analysis(self, content: str) -> Dict[str, Any]:
        """Simple rule-based analysis (placeholder for LLM)."""
        content_lower = content.lower()
        
        # Look for order indicators
        order_indicators = [
            'purchase order', 'po number', 'order number', 'invoice',
            'qty', 'quantity', 'stock code', 'item code', 'delivery',
            'ship to', 'bill to', 'total amount', 'subtotal'
        ]
        
        found_indicators = [indicator for indicator in order_indicators if indicator in content_lower]
        
        is_order = len(found_indicators) >= 3
        confidence = min(len(found_indicators) / 5.0, 1.0)
        
        return {
            'is_order': is_order,
            'confidence': confidence,
            'order_type': 'purchase_order' if is_order else 'unknown',
            'summary': f"Email contains {len(found_indicators)} order indicators",
            'key_indicators': found_indicators
        }
    
    def _simple_extraction(self, content: str, email_data: EmailData) -> Optional[ExtractedOrder]:
        """Simple rule-based extraction (placeholder for LLM)."""
        try:
            # Extract customer name from sender
            sender_parts = email_data.sender.split('<')[0].strip().strip('"')
            customer_name = sender_parts or "Unknown Customer"
            
            # Look for PO number
            po_match = re.search(r'(?:po|purchase order|order)\s*#?\s*(\w+)', content, re.IGNORECASE)
            po_number = po_match.group(1) if po_match else None
            
            # Simple line extraction (this would be much more sophisticated with LLM)
            lines = []
            
            # Look for patterns like "ABC123 x 5" or "Item: ABC123, Qty: 5"
            line_patterns = [
                r'(\w+)\s*x\s*(\d+)',
                r'item:\s*(\w+).*?qty:\s*(\d+)',
                r'stock\s*code:\s*(\w+).*?quantity:\s*(\d+)'
            ]
            
            for pattern in line_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    from ..core.models import OrderLine
                    lines.append(OrderLine(
                        stock_code=match[0],
                        quantity=float(match[1])
                    ))
            
            # If no lines found, create a placeholder
            if not lines:
                from ..core.models import OrderLine
                lines.append(OrderLine(
                    stock_code="UNKNOWN",
                    quantity=1.0
                ))
            
            from ..core.models import CustomerDetails
            extracted_order = ExtractedOrder(
                customer_details=CustomerDetails(customer_name=customer_name),
                order_lines=lines,
                purchase_order_number=po_number
            )
            
            return extracted_order
            
        except Exception as e:
            logger.error(f"Simple extraction failed: {e}")
            return None
