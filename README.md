# TeamsysV0.1 - Unified Email & MYOB Management System

A comprehensive system for processing emails, extracting order data, and integrating with MYOB ERP systems.

## 🏗️ Clean Architecture

This project has been completely restructured with a clean, logical architecture following Python best practices:

```
TeamsysV0.1/
├── __init__.py                 # Main package
├── main.py                     # Application entry point
├── core/                       # Core business logic
│   ├── config.py              # Configuration management
│   ├── models.py              # Data models
│   ├── exceptions.py          # Custom exceptions
│   └── constants.py           # Application constants
├── services/                   # External integrations
│   ├── email.py              # Gmail service
│   ├── erp.py                # MYOB service
│   ├── database.py           # Supabase service
│   ├── llm.py                # LLM service
│   └── monitoring.py         # Health monitoring
├── processors/                 # Business logic processors
│   ├── email_processor.py    # Email processing logic
│   ├── order_processor.py    # Order extraction logic
│   └── report_processor.py   # Report generation
├── cli/                        # Command-line interface
│   ├── main.py               # CLI entry point
│   └── commands.py           # CLI commands
└── utils/                      # Utilities
    ├── pdf.py                # PDF extraction
    ├── json_utils.py         # JSON utilities
    └── validation.py         # Data validation
```

## 🚀 Features

- **Email Processing**: Automated Gmail integration with label-based filtering
- **Order Extraction**: AI-powered order data extraction from emails and PDFs
- **MYOB Integration**: Direct integration with MYOB ERP systems
- **Data Validation**: Comprehensive validation for all data models
- **Health Monitoring**: System health checks and performance monitoring
- **Clean CLI**: Intuitive command-line interface for all operations
- **Modular Design**: Clean separation of concerns with proper dependency injection

## 📦 Installation

### Prerequisites

- Python 3.10 or higher (3.12+ recommended)
- Gmail API credentials
- MYOB API access
- Supabase account
- Mistral AI API key (for LLM features)

### Install from Source

```bash
# Clone the repository
git clone https://github.com/slothmc-ctrl/TeamsysV0.1.git
cd TeamsysV0.1

# Install all dependencies (recommended)
pip install -r requirements.txt

# Alternative: Install as package
pip install -e .
```

## ⚙️ Configuration

Create a `.env` file in the project root:

```env
# Gmail API Configuration
GMAIL_CREDENTIALS_FILE=credentials.json
GMAIL_TOKEN_FILE=token.pickle
GMAIL_LABELS_TO_PROCESS=Brady,RSEA,Woolworths
MAX_GMAIL_RESULTS=40

# LLM Configuration
MISTRAL_API_KEY=your_mistral_api_key
MISTRAL_MODEL=mistral-medium-latest

# MYOB Configuration
EXO_IP=your_myob_server_ip
EXO_PORT=your_myob_port
MYOB_USER=your_myob_username
MYOB_PASSWORD=your_myob_password
MYOB_API_KEY=your_myob_api_key
MYOB_TOKEN=your_myob_token

# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key

# Optional Settings
DEBUG=false
OUTPUT_DIR=./output
```

## 🖥️ Usage

### Command Line Interface

```bash
# Check system status
teamsys status

# Check configuration
teamsys --config-check

# Process emails from specific labels
teamsys email process --labels Brady RSEA --max-emails 10

# Reprocess failed emails
teamsys email process --reprocess

# List MYOB orders
teamsys myob list --limit 50

# Process pending orders
teamsys myob process --all

# Get processing statistics
teamsys stats --days 7
```

### Python API

```python
from core.config import config
from core.models import EmailData
from services.email import GmailService
from processors.email_processor import EmailProcessor

# Initialize services
gmail_service = GmailService()
email_processor = EmailProcessor()

# Process emails
results = email_processor.process_emails_by_labels(
    labels=['Brady', 'RSEA'],
    max_emails_per_label=10
)

print(f"Processed {results['processed_count']} emails")
```

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Test the clean architecture
python test_clean_architecture.py

# Run unit tests (if available)
pytest tests/
```

## 📊 Monitoring

The system includes comprehensive health monitoring:

- **Service Health**: Gmail, MYOB, Database, LLM services
- **System Resources**: CPU, memory, disk usage
- **Processing Statistics**: Email processing rates, success rates
- **Error Tracking**: Detailed error logging and analysis

## 🔧 Development

### Project Structure Benefits

1. **Clear Separation of Concerns**: Each module has a single responsibility
2. **Easy Testing**: Modular design enables comprehensive unit testing
3. **Maintainable**: Clean imports and logical organization
4. **Extensible**: Easy to add new services or processors
5. **Type Safe**: Full Pydantic model validation

### Adding New Features

1. **New Service**: Add to `services/`
2. **New Processor**: Add to `processors/`
3. **New CLI Command**: Add to `cli/commands.py`
4. **New Model**: Add to `core/models.py`

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

For support, please open an issue on GitHub or contact the development team.

---

**Note**: This is a complete rewrite of the original scattered codebase into a clean, maintainable architecture following Python best practices.
