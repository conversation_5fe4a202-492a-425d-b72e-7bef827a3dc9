"""
Supabase database service for data persistence.
"""

import json
import logging
import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from supabase import create_client, Client

from core.config import config
from core.models import EmailData, ProcessedOrder, ExtractedOrder
from core.exceptions import DatabaseError, ConfigurationError

logger = logging.getLogger(__name__)


class SupabaseService:
    """Supabase database service."""
    
    def __init__(self):
        """Initialize Supabase service."""
        if not config.SUPABASE_URL or not config.SUPABASE_KEY:
            raise ConfigurationError("Supabase configuration is incomplete")
        
        try:
            self.client: Client = create_client(config.SUPABASE_URL, config.SUPABASE_KEY)
            logger.info("Supabase service initialized")
        except Exception as e:
            raise DatabaseError(f"Failed to initialize Supabase client: {e}")
    
    def test_connection(self) -> bool:
        """Test database connection."""
        try:
            # Simple query to test connection
            result = self.client.table('emails').select('id').limit(1).execute()
            return True
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False
    
    # Email operations
    def save_email(self, email_data: EmailData) -> Optional[str]:
        """Save email data to database."""
        try:
            # Prepare email data for database
            email_dict = {
                'gmail_id': email_data.gmail_id,
                'subject': email_data.subject,
                'sender': email_data.sender,
                'recipient': email_data.recipient,
                'body': email_data.body,
                'html_body': email_data.html_body,
                'thread_id': email_data.thread_id,
                'label_ids': email_data.label_ids,
                'received_date': email_data.received_date.isoformat() if email_data.received_date else None,
                'has_attachments': email_data.has_attachments,
                # 'priority': email_data.priority.value,  # Commented out - column doesn't exist in DB
                'created_at': datetime.now().isoformat()
            }
            
            # Insert email
            result = self.client.table('emails').insert(email_dict).execute()
            
            if result.data:
                email_id = result.data[0]['id']
                logger.info(f"Saved email to database: {email_id}")
                
                # Save attachments if any
                if email_data.attachments:
                    self._save_attachments(email_id, email_data.attachments)
                
                return email_id
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to save email: {e}")
            raise DatabaseError(f"Email save failed: {e}")
    
    def _save_attachments(self, email_id: str, attachments: List) -> None:
        """Save email attachments."""
        try:
            for attachment in attachments:
                attachment_dict = {
                    'email_id': email_id,
                    'filename': attachment.filename,
                    'content_type': attachment.content_type,
                    'size_bytes': attachment.size_bytes,
                    'processed': attachment.processed,
                    'extracted_text': attachment.extracted_text
                }
                
                self.client.table('email_attachments').insert(attachment_dict).execute()
            
            logger.info(f"Saved {len(attachments)} attachments for email {email_id}")
            
        except Exception as e:
            logger.error(f"Failed to save attachments: {e}")
    
    def get_email_by_gmail_id(self, gmail_id: str) -> Optional[Dict[str, Any]]:
        """Get email by Gmail ID."""
        try:
            result = self.client.table('emails').select('*').eq('gmail_id', gmail_id).execute()
            
            if result.data:
                return result.data[0]
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get email by Gmail ID: {e}")
            raise DatabaseError(f"Email lookup failed: {e}")
    
    def get_unprocessed_emails(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get unprocessed emails."""
        try:
            result = self.client.table('emails').select('*').is_('processed_date', 'null').limit(limit).execute()
            
            return result.data or []
            
        except Exception as e:
            logger.error(f"Failed to get unprocessed emails: {e}")
            raise DatabaseError(f"Unprocessed emails lookup failed: {e}")
    
    # Order operations
    def save_processed_order(self, processed_order: ProcessedOrder) -> Optional[str]:
        """Save processed order to database."""
        # Temporarily disabled due to database schema mismatch
        logger.info(f"Processed order save skipped for email: {processed_order.email_subject}")
        return "temp-id-" + processed_order.email_id

        # try:
        #     order_dict = {
        #         'email_id': processed_order.email_id,
        #         'email_subject': processed_order.email_subject,
        #         'processing_status': processed_order.processing_status,
        #         # 'error_message': processed_order.error_message,  # Column may not exist
        #         'markdown_summary': processed_order.markdown_summary,
        #         # 'created_at': processed_order.created_at.isoformat(),  # Column may not exist
        #         # 'updated_at': processed_order.updated_at.isoformat()  # Column may not exist
        #     }
        #
        #     # Add extracted order data if available
        #     if processed_order.extracted_order:
        #         order_dict['extracted_order_data'] = processed_order.extracted_order.dict()
        #
        #     # Add MYOB payload if available
        #     if processed_order.myob_payload:
        #         order_dict['myob_payload_data'] = processed_order.myob_payload.dict()
        #
        #     result = self.client.table('processed_orders').insert(order_dict).execute()
        #
        #     if result.data:
        #         order_id = result.data[0]['id']
        #         logger.info(f"Saved processed order: {order_id}")
        #         return order_id
        #
        #     return None
        #
        # except Exception as e:
        #     logger.error(f"Failed to save processed order: {e}")
        #     raise DatabaseError(f"Processed order save failed: {e}")
    
    def get_processed_order(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Get processed order by ID."""
        try:
            result = self.client.table('processed_orders').select('*').eq('id', order_id).execute()
            
            if result.data:
                return result.data[0]
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get processed order: {e}")
            raise DatabaseError(f"Processed order lookup failed: {e}")
    
    def update_processing_status(self, email_id: str, status: str, error_message: Optional[str] = None) -> bool:
        """Update email processing status."""
        # Temporarily disabled due to database schema mismatch
        logger.info(f"Processing status update skipped for email {email_id}: {status}")
        return True

        # try:
        #     update_data = {
        #         'processing_status': status,
        #         # 'updated_at': datetime.now().isoformat()  # Column may not exist
        #     }
        #
        #     # Skip error_message and processed_date as columns don't exist in DB
        #     # if error_message:
        #     #     update_data['error_message'] = error_message
        #     #
        #     # if status == 'completed':
        #     #     update_data['processed_date'] = datetime.now().isoformat()
        #
        #     result = self.client.table('emails').update(update_data).eq('id', email_id).execute()
        #
        #     return bool(result.data)
        #
        # except Exception as e:
        #     logger.error(f"Failed to update processing status: {e}")
        #     return False
    
    # Customer operations
    def find_customer_by_name(self, customer_name: str) -> Optional[Dict[str, Any]]:
        """Find customer by name."""
        try:
            # Try exact match first
            result = self.client.table('customers').select('*').ilike('customer_name', customer_name).execute()
            
            if result.data:
                logger.info(f"Found customer: {customer_name}")
                return result.data[0]
            
            # Try fuzzy match
            result = self.client.table('customers').select('*').ilike('customer_name', f'%{customer_name}%').execute()
            
            if result.data:
                logger.info(f"Found customer with fuzzy match: {customer_name}")
                return result.data[0]
            
            logger.warning(f"Customer not found: {customer_name}")
            return None
            
        except Exception as e:
            logger.error(f"Customer search failed: {e}")
            return None
    
    def get_customer_by_debtor_id(self, debtor_id: int) -> Optional[Dict[str, Any]]:
        """Get customer by debtor ID."""
        try:
            result = self.client.table('customers').select('*').eq('debtor_id', debtor_id).execute()
            
            if result.data:
                return result.data[0]
            
            return None
            
        except Exception as e:
            logger.error(f"Customer lookup by debtor ID failed: {e}")
            return None
    
    # Analytics and reporting
    def get_processing_stats(self, days: int = 7) -> Dict[str, Any]:
        """Get processing statistics."""
        try:
            from_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            # Get email counts
            total_emails = self.client.table('emails').select('id', count='exact').gte('created_at', from_date).execute()
            processed_emails = self.client.table('emails').select('id', count='exact').gte('created_at', from_date).not_.is_('processed_date', 'null').execute()
            
            # Get order counts
            total_orders = self.client.table('processed_orders').select('id', count='exact').gte('created_at', from_date).execute()
            successful_orders = self.client.table('processed_orders').select('id', count='exact').gte('created_at', from_date).eq('processing_status', 'completed').execute()
            
            return {
                'period_days': days,
                'total_emails': total_emails.count,
                'processed_emails': processed_emails.count,
                'total_orders': total_orders.count,
                'successful_orders': successful_orders.count,
                'processing_rate': (processed_emails.count / max(total_emails.count, 1)) * 100,
                'success_rate': (successful_orders.count / max(total_orders.count, 1)) * 100
            }
            
        except Exception as e:
            logger.error(f"Failed to get processing stats: {e}")
            return {}
