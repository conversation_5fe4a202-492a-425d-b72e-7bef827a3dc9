"""
MYOB ERP service for order management.
"""

import json
import logging
import requests
from typing import Dict, Any, Optional, List
from datetime import datetime

from core.config import config
from core.models import MYOBPayload, ExtractedOrder
from core.exceptions import MY<PERSON>BError, ConfigurationError

logger = logging.getLogger(__name__)


class MYOBService:
    """MYOB ERP service for order operations."""
    
    def __init__(self):
        """Initialize MYOB service."""
        if not config.MYOB_BASE_URL:
            raise ConfigurationError("MYOB configuration is incomplete")
        
        self.base_url = config.MYOB_BASE_URL
        self.headers = config.MYOB_HEADERS
        self.timeout = 30
        
        logger.info(f"MYOB service initialized with base URL: {self.base_url}")
    
    def validate_connection(self) -> bool:
        """Validate MYOB API connection."""
        try:
            response = requests.get(
                f"{self.base_url}/ping",
                headers=self.headers,
                timeout=self.timeout
            )
            return response.status_code == 200
        except Exception as e:
            logger.error(f"MYOB connection validation failed: {e}")
            return False
    
    def search_customer(self, customer_name: str) -> Optional[Dict[str, Any]]:
        """Search for customer by name."""
        try:
            response = requests.get(
                f"{self.base_url}/debtor/search",
                headers=self.headers,
                params={'q': customer_name},
                timeout=self.timeout
            )
            response.raise_for_status()
            
            results = response.json()
            if results:
                logger.info(f"Found {len(results)} customers matching '{customer_name}'")
                return results[0]  # Return first match
            
            logger.warning(f"No customers found matching '{customer_name}'")
            return None
            
        except requests.exceptions.RequestException as e:
            logger.error(f"MYOB customer search failed: {e}")
            raise MYOBError(f"Customer search failed: {e}")
    
    def get_customer_by_id(self, debtor_id: int) -> Optional[Dict[str, Any]]:
        """Get customer details by debtor ID."""
        try:
            response = requests.get(
                f"{self.base_url}/debtor/{debtor_id}",
                headers=self.headers,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            customer = response.json()
            logger.info(f"Retrieved customer details for debtor ID: {debtor_id}")
            return customer
            
        except requests.exceptions.RequestException as e:
            logger.error(f"MYOB customer lookup failed: {e}")
            raise MYOBError(f"Customer lookup failed: {e}")
    
    def validate_order(self, order_data: ExtractedOrder) -> Optional[MYOBPayload]:
        """Validate order data and prepare MYOB payload."""
        try:
            # Find customer
            customer = None
            if order_data.customer_details.debtor_id:
                customer = self.get_customer_by_id(order_data.customer_details.debtor_id)
            else:
                customer = self.search_customer(order_data.customer_details.customer_name)
            
            if not customer:
                logger.error(f"Customer not found: {order_data.customer_details.customer_name}")
                return None
            
            debtor_id = customer.get('debtorid') or customer.get('id')
            if not debtor_id:
                logger.error("Customer record missing debtor ID")
                return None
            
            # Prepare order lines
            lines = []
            for line in order_data.order_lines:
                myob_line = {
                    'stockcode': line.stock_code,
                    'orderquantity': line.quantity
                }
                
                if line.unit_price is not None:
                    myob_line['unitprice'] = line.unit_price
                
                if line.description:
                    myob_line['description'] = line.description
                
                lines.append(myob_line)
            
            # Prepare delivery address
            delivery_address = None
            if order_data.delivery_address:
                delivery_address = {
                    'line1': order_data.delivery_address.line1,
                    'line2': order_data.delivery_address.line2,
                    'line3': order_data.delivery_address.city,
                    'line4': order_data.delivery_address.state,
                    'line5': order_data.delivery_address.postal_code,
                    'line6': order_data.delivery_address.country
                }
            
            # Create MYOB payload
            payload = MYOBPayload(
                debtorid=debtor_id,
                customerordernumber=order_data.purchase_order_number,
                status=0,  # Draft
                defaultlocationid=1,
                lines=lines,
                deliveryaddress=delivery_address
            )
            
            logger.info(f"Validated order for customer {customer.get('name', 'Unknown')}")
            return payload
            
        except Exception as e:
            logger.error(f"Order validation failed: {e}")
            raise MYOBError(f"Order validation failed: {e}")
    
    def create_order(self, payload: MYOBPayload) -> Optional[Dict[str, Any]]:
        """Create order in MYOB."""
        try:
            response = requests.post(
                f"{self.base_url}/salesorder/",
                json=payload.dict(),
                headers=self.headers,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"Created MYOB order: {result.get('id', 'Unknown ID')}")
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"MYOB order creation failed: {e}")
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"Response: {e.response.text}")
            raise MYOBError(f"Order creation failed: {e}")
    
    def get_order(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Get order details by ID."""
        try:
            response = requests.get(
                f"{self.base_url}/salesorder/{order_id}",
                headers=self.headers,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            order = response.json()
            logger.info(f"Retrieved order: {order_id}")
            return order
            
        except requests.exceptions.RequestException as e:
            logger.error(f"MYOB order lookup failed: {e}")
            raise MYOBError(f"Order lookup failed: {e}")
    
    def list_orders(self, limit: int = 50, status: Optional[int] = None) -> List[Dict[str, Any]]:
        """List orders with optional filtering."""
        try:
            params = {'limit': limit}
            if status is not None:
                params['status'] = status
            
            response = requests.get(
                f"{self.base_url}/salesorder/",
                headers=self.headers,
                params=params,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            orders = response.json()
            logger.info(f"Retrieved {len(orders)} orders")
            return orders
            
        except requests.exceptions.RequestException as e:
            logger.error(f"MYOB order listing failed: {e}")
            raise MYOBError(f"Order listing failed: {e}")
    
    def update_order_status(self, order_id: str, status: int) -> bool:
        """Update order status."""
        try:
            response = requests.patch(
                f"{self.base_url}/salesorder/{order_id}",
                json={'status': status},
                headers=self.headers,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            logger.info(f"Updated order {order_id} status to {status}")
            return True
            
        except requests.exceptions.RequestException as e:
            logger.error(f"MYOB order status update failed: {e}")
            raise MYOBError(f"Order status update failed: {e}")
    
    def get_stock_info(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """Get stock information by code."""
        try:
            response = requests.get(
                f"{self.base_url}/stock/{stock_code}",
                headers=self.headers,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            stock = response.json()
            logger.info(f"Retrieved stock info for: {stock_code}")
            return stock
            
        except requests.exceptions.RequestException as e:
            logger.error(f"MYOB stock lookup failed: {e}")
            return None
