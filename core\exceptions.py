"""
Custom exceptions for TeamsysV0.1
"""


class TeamsysException(Exception):
    """Base exception for all Teamsys errors."""
    pass


class ConfigurationError(TeamsysException):
    """Raised when configuration is invalid or missing."""
    pass


class EmailProcessingError(TeamsysException):
    """Raised when email processing fails."""
    pass


class LLMServiceError(TeamsysException):
    """Raised when LLM service encounters an error."""
    pass


class DatabaseError(TeamsysException):
    """Raised when database operations fail."""
    pass


class MYOBError(TeamsysException):
    """Raised when MYOB API operations fail."""
    pass


class ValidationError(TeamsysException):
    """Raised when data validation fails."""
    pass


class PDFExtractionError(TeamsysException):
    """Raised when PDF text extraction fails."""
    pass


class AuthenticationError(TeamsysException):
    """Raised when authentication fails."""
    pass


class NetworkError(TeamsysException):
    """Raised when network operations fail."""
    pass
