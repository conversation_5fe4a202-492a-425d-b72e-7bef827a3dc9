#!/usr/bin/env python3
"""
Main CLI entry point for TeamsysV0.1
"""

import argparse
import asyncio
import logging
import sys
from typing import Optional

from core.config import config
from cli.commands import CLICommands

logger = logging.getLogger(__name__)


def create_parser():
    """Create the main argument parser."""
    parser = argparse.ArgumentParser(
        description="TeamsysV0.1 - Unified Email & MYOB Management System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process emails from specific labels
  teamsys email process --labels Brady RSEA --max-emails 10
  
  # Check system status
  teamsys status
  
  # List pending MYOB orders
  teamsys myob list
  
  # Process all pending orders
  teamsys myob process --all
  
  # Get processing statistics
  teamsys stats --days 7
        """
    )
    
    # Global options
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug logging')
    parser.add_argument('--config-check', action='store_true',
                       help='Check configuration and exit')
    
    # Create subparsers
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Email command
    email_parser = subparsers.add_parser('email', help='Email processing commands')
    email_subparsers = email_parser.add_subparsers(dest='email_action')
    
    # Email process command
    email_process = email_subparsers.add_parser('process', help='Process emails')
    email_process.add_argument('--labels', nargs='+', 
                              help='Gmail labels to process')
    email_process.add_argument('--max-emails', type=int, default=20,
                              help='Maximum emails to process per label')
    email_process.add_argument('--reprocess', action='store_true',
                              help='Reprocess failed emails')
    
    # MYOB command
    myob_parser = subparsers.add_parser('myob', help='MYOB management commands')
    myob_subparsers = myob_parser.add_subparsers(dest='myob_action')
    
    # MYOB list command
    myob_list = myob_subparsers.add_parser('list', help='List orders')
    myob_list.add_argument('--status', type=int, help='Filter by status')
    myob_list.add_argument('--limit', type=int, default=50, help='Number of orders to show')
    myob_list.add_argument('--format', choices=['table', 'json'], default='table')
    
    # MYOB process command
    myob_process = myob_subparsers.add_parser('process', help='Process orders')
    myob_process.add_argument('--all', action='store_true', help='Process all pending orders')
    myob_process.add_argument('--order-id', help='Process specific order ID')
    
    # Status command
    status_parser = subparsers.add_parser('status', help='Check system status')
    status_parser.add_argument('--detailed', action='store_true', help='Show detailed status')
    
    # Stats command
    stats_parser = subparsers.add_parser('stats', help='Show processing statistics')
    stats_parser.add_argument('--days', type=int, default=7, help='Number of days to analyze')
    
    return parser


def setup_logging(args):
    """Setup logging based on command line arguments."""
    if args.debug:
        level = logging.DEBUG
    elif args.verbose:
        level = logging.INFO
    else:
        level = logging.WARNING
    
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def check_configuration():
    """Check and validate configuration."""
    print("🔧 Checking configuration...")
    
    errors = config.validate()
    
    if errors:
        print("❌ Configuration errors found:")
        for error in errors:
            print(f"  - {error}")
        return False
    else:
        print("✅ Configuration is valid")
        
        # Create output directories
        config.ensure_directories()
        print("✅ Output directories created")
        
        return True


async def main():
    """Main CLI entry point."""
    parser = create_parser()
    args = parser.parse_args()
    
    # Show help if no command specified
    if not args.command:
        parser.print_help()
        return False
    
    # Setup logging
    setup_logging(args)
    
    # Check configuration if requested
    if args.config_check:
        return check_configuration()
    
    # Validate configuration
    if not check_configuration():
        print("❌ Configuration validation failed. Use --config-check for details.")
        return False
    
    try:
        # Initialize CLI commands
        commands = CLICommands()
        
        # Route to appropriate command handler
        if args.command == 'email':
            return await commands.handle_email_command(args)
            
        elif args.command == 'myob':
            return await commands.handle_myob_command(args)
            
        elif args.command == 'status':
            return commands.handle_status_command(args)
            
        elif args.command == 'stats':
            return commands.handle_stats_command(args)
            
        else:
            print(f"Unknown command: {args.command}")
            parser.print_help()
            return False
            
    except KeyboardInterrupt:
        print("\n👋 Operation cancelled by user")
        return False
    except Exception as e:
        logger.error(f"Command failed: {e}")
        if args.debug:
            raise
        print(f"❌ Command failed: {e}")
        return False


def cli_main():
    """Synchronous wrapper for the main CLI function."""
    try:
        return asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Operation cancelled by user")
        return False


if __name__ == "__main__":
    success = cli_main()
    sys.exit(0 if success else 1)
