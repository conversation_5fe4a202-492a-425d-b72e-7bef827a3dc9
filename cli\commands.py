"""
CLI command implementations for TeamsysV0.1
"""

import json
import logging
from typing import Any, Dict
from datetime import datetime

from core.config import config
from processors.email_processor import EmailProcessor
from services.erp import MYOBService
from services.database import SupabaseService
from services.monitoring import HealthMonitor

logger = logging.getLogger(__name__)


class CLICommands:
    """CLI command implementations."""
    
    def __init__(self):
        """Initialize CLI commands."""
        self.email_processor = None
        self.myob_service = None
        self.db_service = None
        self.health_monitor = None
        
        logger.info("CLI commands initialized")
    
    def _lazy_init_services(self):
        """Lazy initialization of services."""
        if not self.email_processor:
            self.email_processor = EmailProcessor()
        if not self.myob_service:
            self.myob_service = MYOBService()
        if not self.db_service:
            self.db_service = SupabaseService()
        if not self.health_monitor:
            self.health_monitor = HealthMonitor()
    
    async def handle_email_command(self, args) -> bool:
        """Handle email processing commands."""
        if not args.email_action:
            print("Please specify an email action: process")
            return False
        
        if args.email_action == 'process':
            return await self._handle_email_process(args)
        
        return False
    
    async def _handle_email_process(self, args) -> bool:
        """Handle email processing."""
        try:
            self._lazy_init_services()
            
            if args.reprocess:
                print("🔄 Reprocessing failed emails...")
                results = self.email_processor.reprocess_failed_emails(limit=50)
            else:
                labels = args.labels or config.GMAIL_LABELS_TO_PROCESS
                max_emails = args.max_emails
                
                print(f"📧 Processing emails from labels: {', '.join(labels)}")
                print(f"Max emails per label: {max_emails}")
                
                results = self.email_processor.process_emails_by_labels(
                    labels=labels,
                    max_emails_per_label=max_emails
                )
            
            # Display results
            self._display_email_results(results)
            return True
            
        except Exception as e:
            logger.error(f"Email processing failed: {e}")
            print(f"❌ Email processing failed: {e}")
            return False
    
    def _display_email_results(self, results: Dict[str, Any]):
        """Display email processing results."""
        print("\n📊 Email Processing Results")
        print("=" * 40)
        print(f"Total processed: {results['processed_count']}")
        print(f"Total errors: {results['error_count']}")
        
        if results.get('labels_processed'):
            print("\nBy Label:")
            for label_result in results['labels_processed']:
                print(f"  {label_result['label']}: {label_result['processed']}/{label_result['emails_found']} processed")
        
        if results.get('errors'):
            print("\nErrors:")
            for error in results['errors'][:5]:  # Show first 5 errors
                print(f"  - {error}")
            if len(results['errors']) > 5:
                print(f"  ... and {len(results['errors']) - 5} more errors")
    
    async def handle_myob_command(self, args) -> bool:
        """Handle MYOB management commands."""
        if not args.myob_action:
            print("Please specify a MYOB action: list, process")
            return False
        
        if args.myob_action == 'list':
            return await self._handle_myob_list(args)
        elif args.myob_action == 'process':
            return await self._handle_myob_process(args)
        
        return False
    
    async def _handle_myob_list(self, args) -> bool:
        """Handle MYOB order listing."""
        try:
            self._lazy_init_services()
            
            orders = self.myob_service.list_orders(
                limit=args.limit,
                status=args.status
            )
            
            if args.format == 'json':
                print(json.dumps(orders, indent=2, default=str))
            else:
                self._display_orders_table(orders)
            
            return True
            
        except Exception as e:
            logger.error(f"MYOB list failed: {e}")
            print(f"❌ MYOB list failed: {e}")
            return False
    
    def _display_orders_table(self, orders: list):
        """Display orders in table format."""
        if not orders:
            print("No orders found.")
            return
        
        print(f"\n📋 MYOB Orders ({len(orders)} found)")
        print("=" * 80)
        print(f"{'ID':<10} {'Customer':<20} {'Status':<10} {'Amount':<12} {'Date':<12}")
        print("-" * 80)
        
        for order in orders[:20]:  # Show first 20
            order_id = str(order.get('id', 'N/A'))[:9]
            customer = str(order.get('customer_name', 'Unknown'))[:19]
            status = str(order.get('status', 'N/A'))[:9]
            amount = f"${order.get('total_amount', 0):.2f}"[:11]
            date = str(order.get('created_date', 'N/A'))[:11]
            
            print(f"{order_id:<10} {customer:<20} {status:<10} {amount:<12} {date:<12}")
        
        if len(orders) > 20:
            print(f"\n... and {len(orders) - 20} more orders")
    
    async def _handle_myob_process(self, args) -> bool:
        """Handle MYOB order processing."""
        try:
            self._lazy_init_services()
            
            if args.all:
                print("🏢 Processing all pending orders...")
                # This would implement batch processing
                print("Batch processing not yet implemented")
                return True
            elif args.order_id:
                print(f"🏢 Processing order: {args.order_id}")
                # This would implement single order processing
                print("Single order processing not yet implemented")
                return True
            else:
                print("Please specify --all or --order-id")
                return False
                
        except Exception as e:
            logger.error(f"MYOB processing failed: {e}")
            print(f"❌ MYOB processing failed: {e}")
            return False
    
    def handle_status_command(self, args) -> bool:
        """Handle system status command."""
        try:
            self._lazy_init_services()
            
            print("📊 System Status Check")
            print("=" * 40)
            
            health_summary = self.health_monitor.get_health_summary()
            overall_status = health_summary['overall_status']
            
            # Display overall status
            status_emoji = {
                'healthy': '✅',
                'degraded': '⚠️',
                'unhealthy': '❌',
                'unknown': '❓'
            }
            
            print(f"Overall Status: {status_emoji.get(overall_status, '❓')} {overall_status.upper()}")
            print(f"Last Check: {health_summary['timestamp']}")
            print()
            
            # Display service statuses
            services = health_summary['services']
            for service_name, service_status in services.items():
                emoji = status_emoji.get(service_status['status'], '❓')
                print(f"{emoji} {service_name.title()}: {service_status['status']}")
                
                if service_status.get('response_time_ms'):
                    print(f"    Response Time: {service_status['response_time_ms']:.1f}ms")
                
                if service_status.get('error_message'):
                    print(f"    Error: {service_status['error_message']}")
                
                if args.detailed and service_status.get('details'):
                    print(f"    Details: {service_status['details']}")
                
                print()
            
            # Display summary
            summary = health_summary['summary']
            print(f"Services: {summary['healthy']} healthy, {summary['degraded']} degraded, {summary['unhealthy']} unhealthy")
            
            return overall_status in ['healthy', 'degraded']
            
        except Exception as e:
            logger.error(f"Status check failed: {e}")
            print(f"❌ Status check failed: {e}")
            return False
    
    def handle_stats_command(self, args) -> bool:
        """Handle statistics command."""
        try:
            self._lazy_init_services()
            
            stats = self.email_processor.get_processing_stats(args.days)
            
            print(f"📈 Processing Statistics (Last {args.days} days)")
            print("=" * 50)
            
            if stats:
                print(f"Total Emails: {stats.get('total_emails', 0)}")
                print(f"Processed Emails: {stats.get('processed_emails', 0)}")
                print(f"Processing Rate: {stats.get('processing_rate', 0):.1f}%")
                print()
                print(f"Total Orders: {stats.get('total_orders', 0)}")
                print(f"Successful Orders: {stats.get('successful_orders', 0)}")
                print(f"Success Rate: {stats.get('success_rate', 0):.1f}%")
            else:
                print("No statistics available")
            
            return True
            
        except Exception as e:
            logger.error(f"Stats command failed: {e}")
            print(f"❌ Stats command failed: {e}")
            return False
