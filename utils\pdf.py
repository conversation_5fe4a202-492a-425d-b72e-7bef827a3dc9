"""
PDF text extraction utilities.
"""

import logging
from typing import Optional
import io

try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False

try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False

from core.exceptions import PDFExtractionError

logger = logging.getLogger(__name__)


def extract_text_from_pdf(pdf_data: bytes) -> Optional[str]:
    """
    Extract text from PDF data using available libraries.
    
    Args:
        pdf_data: PDF file content as bytes
        
    Returns:
        Extracted text or None if extraction fails
        
    Raises:
        PDFExtractionError: If extraction fails
    """
    if not pdf_data:
        return None
    
    # Try pdfplumber first (generally better text extraction)
    if PDFPLUMBER_AVAILABLE:
        try:
            text = _extract_with_pdfplumber(pdf_data)
            if text and text.strip():
                logger.info("Successfully extracted text using pdfplumber")
                return text.strip()
        except Exception as e:
            logger.warning(f"pdfplumber extraction failed: {e}")
    
    # Fallback to PyPDF2
    if PYPDF2_AVAILABLE:
        try:
            text = _extract_with_pypdf2(pdf_data)
            if text and text.strip():
                logger.info("Successfully extracted text using PyPDF2")
                return text.strip()
        except Exception as e:
            logger.warning(f"PyPDF2 extraction failed: {e}")
    
    # If no libraries available
    if not PYPDF2_AVAILABLE and not PDFPLUMBER_AVAILABLE:
        raise PDFExtractionError("No PDF extraction libraries available. Install PyPDF2 or pdfplumber.")
    
    logger.error("All PDF extraction methods failed")
    return None


def _extract_with_pdfplumber(pdf_data: bytes) -> Optional[str]:
    """Extract text using pdfplumber."""
    import pdfplumber
    
    text_parts = []
    
    with io.BytesIO(pdf_data) as pdf_file:
        with pdfplumber.open(pdf_file) as pdf:
            for page_num, page in enumerate(pdf.pages):
                try:
                    page_text = page.extract_text()
                    if page_text:
                        text_parts.append(page_text)
                        logger.debug(f"Extracted text from page {page_num + 1}")
                except Exception as e:
                    logger.warning(f"Failed to extract text from page {page_num + 1}: {e}")
                    continue
    
    return "\n\n".join(text_parts) if text_parts else None


def _extract_with_pypdf2(pdf_data: bytes) -> Optional[str]:
    """Extract text using PyPDF2."""
    import PyPDF2
    
    text_parts = []
    
    with io.BytesIO(pdf_data) as pdf_file:
        try:
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            
            for page_num, page in enumerate(pdf_reader.pages):
                try:
                    page_text = page.extract_text()
                    if page_text:
                        text_parts.append(page_text)
                        logger.debug(f"Extracted text from page {page_num + 1}")
                except Exception as e:
                    logger.warning(f"Failed to extract text from page {page_num + 1}: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"PyPDF2 reader initialization failed: {e}")
            return None
    
    return "\n\n".join(text_parts) if text_parts else None


def validate_pdf_data(pdf_data: bytes) -> bool:
    """
    Validate that the data is a valid PDF.
    
    Args:
        pdf_data: PDF file content as bytes
        
    Returns:
        True if valid PDF, False otherwise
    """
    if not pdf_data:
        return False
    
    # Check PDF header
    if not pdf_data.startswith(b'%PDF-'):
        return False
    
    # Try to open with available library
    if PDFPLUMBER_AVAILABLE:
        try:
            import pdfplumber
            with io.BytesIO(pdf_data) as pdf_file:
                with pdfplumber.open(pdf_file) as pdf:
                    # Just check if we can access pages
                    return len(pdf.pages) > 0
        except Exception:
            pass
    
    if PYPDF2_AVAILABLE:
        try:
            import PyPDF2
            with io.BytesIO(pdf_data) as pdf_file:
                pdf_reader = PyPDF2.PdfReader(pdf_file)
                return len(pdf_reader.pages) > 0
        except Exception:
            pass
    
    return False


def get_pdf_info(pdf_data: bytes) -> dict:
    """
    Get basic information about a PDF.
    
    Args:
        pdf_data: PDF file content as bytes
        
    Returns:
        Dictionary with PDF information
    """
    info = {
        'valid': False,
        'pages': 0,
        'size_bytes': len(pdf_data) if pdf_data else 0,
        'extractable': False
    }
    
    if not validate_pdf_data(pdf_data):
        return info
    
    info['valid'] = True
    
    # Get page count and test extraction
    if PDFPLUMBER_AVAILABLE:
        try:
            import pdfplumber
            with io.BytesIO(pdf_data) as pdf_file:
                with pdfplumber.open(pdf_file) as pdf:
                    info['pages'] = len(pdf.pages)
                    
                    # Test if we can extract text from first page
                    if pdf.pages:
                        test_text = pdf.pages[0].extract_text()
                        info['extractable'] = bool(test_text and test_text.strip())
                        
        except Exception as e:
            logger.debug(f"pdfplumber info extraction failed: {e}")
    
    elif PYPDF2_AVAILABLE:
        try:
            import PyPDF2
            with io.BytesIO(pdf_data) as pdf_file:
                pdf_reader = PyPDF2.PdfReader(pdf_file)
                info['pages'] = len(pdf_reader.pages)
                
                # Test if we can extract text from first page
                if pdf_reader.pages:
                    test_text = pdf_reader.pages[0].extract_text()
                    info['extractable'] = bool(test_text and test_text.strip())
                    
        except Exception as e:
            logger.debug(f"PyPDF2 info extraction failed: {e}")
    
    return info
