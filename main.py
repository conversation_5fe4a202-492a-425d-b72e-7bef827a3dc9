#!/usr/bin/env python3
"""
Main entry point for TeamsysV0.1
Clean entry point with proper package imports
"""
import sys
from pathlib import Path

# Add the project root to Python path for development
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def main():
    """Main entry point"""
    print("🚀 Starting TeamsysV0.1...")
    print("=" * 50)

    try:
        # Import and run the main CLI
        from cli.main import cli_main
        return cli_main()
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please ensure the teamsys package is properly installed.")
        print("Run: pip install -e . from the project root")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)