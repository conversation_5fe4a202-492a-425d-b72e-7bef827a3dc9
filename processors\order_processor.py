"""
Order processor for handling order validation and MYOB integration.
"""

import logging
from typing import Optional, Dict, Any

from core.models import ExtractedOrder, MYOBPayload, ProcessedOrder
from core.exceptions import MYOBError, ValidationError
from services.erp import MYOBService
from services.database import SupabaseService
from utils.validation import validate_order_data

logger = logging.getLogger(__name__)


class OrderProcessor:
    """Order processing and MYOB integration."""
    
    def __init__(self):
        """Initialize order processor."""
        self.myob_service = MYOBService()
        self.db_service = SupabaseService()
        
        logger.info("Order processor initialized")
    
    def process_order(self, extracted_order: ExtractedOrder, 
                     email_id: str) -> Optional[Dict[str, Any]]:
        """
        Process an extracted order through validation and MYOB integration.
        
        Args:
            extracted_order: Extracted order data
            email_id: Associated email ID
            
        Returns:
            Processing result dictionary
        """
        try:
            logger.info(f"Processing order for customer: {extracted_order.customer_details.customer_name}")
            
            # Validate order data
            is_valid, validation_errors = validate_order_data(extracted_order)
            if not is_valid:
                error_msg = f"Order validation failed: {'; '.join(validation_errors)}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg,
                    'validation_errors': validation_errors
                }
            
            # Validate with MYOB (check customer exists, stock codes, etc.)
            myob_payload = self.myob_service.validate_order(extracted_order)
            if not myob_payload:
                error_msg = "MYOB validation failed - customer or stock codes not found"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg
                }
            
            # Create order in MYOB
            myob_result = self.myob_service.create_order(myob_payload)
            if not myob_result:
                error_msg = "Failed to create order in MYOB"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg
                }
            
            logger.info(f"Successfully created MYOB order: {myob_result.get('id')}")
            
            return {
                'success': True,
                'myob_order_id': myob_result.get('id'),
                'myob_payload': myob_payload.dict(),
                'myob_response': myob_result
            }
            
        except Exception as e:
            error_msg = f"Order processing failed: {e}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }
    
    def validate_customer(self, customer_name: str) -> Optional[Dict[str, Any]]:
        """
        Validate customer exists in MYOB.
        
        Args:
            customer_name: Customer name to validate
            
        Returns:
            Customer data if found, None otherwise
        """
        try:
            customer = self.myob_service.search_customer(customer_name)
            if customer:
                logger.info(f"Customer validated: {customer_name}")
                return customer
            else:
                logger.warning(f"Customer not found: {customer_name}")
                return None
                
        except Exception as e:
            logger.error(f"Customer validation failed: {e}")
            return None
    
    def validate_stock_codes(self, stock_codes: list) -> Dict[str, Any]:
        """
        Validate stock codes exist in MYOB.
        
        Args:
            stock_codes: List of stock codes to validate
            
        Returns:
            Dictionary with validation results
        """
        results = {
            'valid_codes': [],
            'invalid_codes': [],
            'stock_info': {}
        }
        
        for stock_code in stock_codes:
            try:
                stock_info = self.myob_service.get_stock_info(stock_code)
                if stock_info:
                    results['valid_codes'].append(stock_code)
                    results['stock_info'][stock_code] = stock_info
                    logger.debug(f"Stock code validated: {stock_code}")
                else:
                    results['invalid_codes'].append(stock_code)
                    logger.warning(f"Invalid stock code: {stock_code}")
                    
            except Exception as e:
                logger.error(f"Stock code validation failed for {stock_code}: {e}")
                results['invalid_codes'].append(stock_code)
        
        return results
    
    def get_order_status(self, myob_order_id: str) -> Optional[Dict[str, Any]]:
        """
        Get order status from MYOB.
        
        Args:
            myob_order_id: MYOB order ID
            
        Returns:
            Order status information
        """
        try:
            order = self.myob_service.get_order(myob_order_id)
            if order:
                return {
                    'order_id': myob_order_id,
                    'status': order.get('status'),
                    'customer': order.get('customer_name'),
                    'total': order.get('total_amount'),
                    'created_date': order.get('created_date')
                }
            return None
            
        except Exception as e:
            logger.error(f"Failed to get order status: {e}")
            return None
    
    def update_order_status(self, myob_order_id: str, new_status: int) -> bool:
        """
        Update order status in MYOB.
        
        Args:
            myob_order_id: MYOB order ID
            new_status: New status value
            
        Returns:
            True if successful, False otherwise
        """
        try:
            success = self.myob_service.update_order_status(myob_order_id, new_status)
            if success:
                logger.info(f"Updated order {myob_order_id} status to {new_status}")
            return success
            
        except Exception as e:
            logger.error(f"Failed to update order status: {e}")
            return False
    
    def get_pending_orders(self, limit: int = 50) -> list:
        """
        Get pending orders from MYOB.
        
        Args:
            limit: Maximum number of orders to return
            
        Returns:
            List of pending orders
        """
        try:
            # Status 0 = Draft/Pending
            orders = self.myob_service.list_orders(limit=limit, status=0)
            logger.info(f"Retrieved {len(orders)} pending orders")
            return orders
            
        except Exception as e:
            logger.error(f"Failed to get pending orders: {e}")
            return []
    
    def process_pending_orders(self, limit: int = 50) -> Dict[str, Any]:
        """
        Process all pending orders (confirm them).
        
        Args:
            limit: Maximum number of orders to process
            
        Returns:
            Processing results
        """
        results = {
            'processed': 0,
            'failed': 0,
            'errors': []
        }
        
        try:
            pending_orders = self.get_pending_orders(limit)
            
            for order in pending_orders:
                order_id = order.get('id')
                if not order_id:
                    continue
                
                try:
                    # Update status to confirmed (1)
                    success = self.update_order_status(order_id, 1)
                    if success:
                        results['processed'] += 1
                        logger.info(f"Confirmed order: {order_id}")
                    else:
                        results['failed'] += 1
                        results['errors'].append(f"Failed to confirm order {order_id}")
                        
                except Exception as e:
                    error_msg = f"Error processing order {order_id}: {e}"
                    logger.error(error_msg)
                    results['failed'] += 1
                    results['errors'].append(error_msg)
            
            logger.info(f"Batch processing complete: {results['processed']} processed, {results['failed']} failed")
            return results
            
        except Exception as e:
            error_msg = f"Batch processing failed: {e}"
            logger.error(error_msg)
            results['errors'].append(error_msg)
            return results
