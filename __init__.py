"""
TeamsysV0.1 - Unified Email & MYOB Management System

A comprehensive system for processing emails, extracting order data,
and integrating with MYOB ERP systems.

Clean architecture with proper separation of concerns.
"""

__version__ = "0.1.0"
__author__ = "Team Systems"
__description__ = "Unified Email & MYOB Management System"

# Core imports
from core.config import config
from core.models import EmailData, ExtractedOrder, ProcessedOrder, MYOBPayload

# Service imports
from services.email import GmailService
from services.erp import MYOBService
from services.database import SupabaseService
from services.llm import LLMService

__all__ = [
    # Metadata
    "__version__", "__author__", "__description__",
    # Core
    "config", "EmailData", "ExtractedOrder", "ProcessedOrder", "MYOBPayload",
    # Services
    "GmailService", "MYOBService", "SupabaseService", "LLMService"
]
