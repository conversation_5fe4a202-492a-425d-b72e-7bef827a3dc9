"""
Gmail service for email operations.
"""

import os
import base64
import logging
import pickle
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from bs4 import BeautifulSoup

from core.config import config
from core.models import EmailData, EmailAttachment
from core.exceptions import EmailProcessingError, AuthenticationError

logger = logging.getLogger(__name__)


class GmailService:
    """Gmail API service for email operations."""
    
    def __init__(self):
        """Initialize Gmail service."""
        self.service = None
        self._authenticate()
    
    def _authenticate(self):
        """Authenticate with Gmail API."""
        creds = None
        
        # Load existing token
        if os.path.exists(config.GMAIL_TOKEN_FILE):
            try:
                with open(config.GMAIL_TOKEN_FILE, 'rb') as token:
                    creds = pickle.load(token)
                logger.info("Loaded existing Gmail credentials")
            except Exception as e:
                logger.warning(f"Failed to load existing token: {e}")
                creds = None
        
        # Refresh or get new credentials
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                try:
                    creds.refresh(Request())
                    logger.info("Refreshed Gmail credentials")
                except Exception as e:
                    logger.error(f"Failed to refresh credentials: {e}")
                    creds = None
            
            if not creds:
                if not os.path.exists(config.GMAIL_CREDENTIALS_FILE):
                    raise AuthenticationError(f"Gmail credentials file not found: {config.GMAIL_CREDENTIALS_FILE}")
                
                flow = InstalledAppFlow.from_client_secrets_file(
                    config.GMAIL_CREDENTIALS_FILE, config.GMAIL_SCOPES
                )
                creds = flow.run_local_server(port=8080)
                logger.info("Obtained new Gmail credentials")
            
            # Save credentials
            with open(config.GMAIL_TOKEN_FILE, 'wb') as token:
                pickle.dump(creds, token)
        
        try:
            self.service = build('gmail', 'v1', credentials=creds)
            logger.info("Gmail service initialized successfully")
        except Exception as e:
            raise AuthenticationError(f"Failed to build Gmail service: {e}")
    
    def get_emails_by_label(self, label_name: str, max_results: int = 10, 
                           time_filter_hours: Optional[int] = None) -> List[EmailData]:
        """Get emails from a specific label."""
        try:
            # Build query
            query = f"label:{label_name}"
            if time_filter_hours:
                after_date = datetime.now() - timedelta(hours=time_filter_hours)
                query += f" after:{after_date.strftime('%Y/%m/%d')}"
            
            # Search for messages
            results = self.service.users().messages().list(
                userId='me',
                q=query,
                maxResults=max_results
            ).execute()
            
            messages = results.get('messages', [])
            logger.info(f"Found {len(messages)} emails in label '{label_name}'")
            
            # Get full email data
            emails = []
            for message in messages:
                try:
                    email_data = self._get_email_data(message['id'])
                    if email_data:
                        emails.append(email_data)
                except Exception as e:
                    logger.error(f"Failed to process email {message['id']}: {e}")
                    continue
            
            return emails
            
        except HttpError as e:
            logger.error(f"Gmail API error: {e}")
            raise EmailProcessingError(f"Failed to get emails: {e}")
        except Exception as e:
            logger.error(f"Unexpected error getting emails: {e}")
            raise EmailProcessingError(f"Unexpected error: {e}")
    
    def _get_email_data(self, message_id: str) -> Optional[EmailData]:
        """Get full email data for a message."""
        try:
            message = self.service.users().messages().get(
                userId='me', 
                id=message_id,
                format='full'
            ).execute()
            
            # Extract headers
            headers = {h['name']: h['value'] for h in message['payload'].get('headers', [])}
            
            # Extract body
            body_text = self._extract_body(message['payload'])
            
            # Extract attachments
            attachments = self._extract_attachments(message_id, message['payload'])
            
            # Create EmailData object
            email_data = EmailData(
                gmail_id=message_id,
                subject=headers.get('Subject', 'No Subject'),
                sender=headers.get('From', 'Unknown'),
                recipient=headers.get('To'),
                body=body_text,
                thread_id=message.get('threadId'),
                label_ids=message.get('labelIds', []),
                received_date=self._parse_date(headers.get('Date')),
                has_attachments=len(attachments) > 0,
                attachments=attachments
            )
            
            return email_data
            
        except Exception as e:
            logger.error(f"Failed to get email data for {message_id}: {e}")
            return None
    
    def _extract_body(self, payload: Dict[str, Any]) -> str:
        """Extract email body text."""
        body = ""
        
        if 'parts' in payload:
            for part in payload['parts']:
                if part['mimeType'] == 'text/plain':
                    data = part['body'].get('data')
                    if data:
                        body = base64.urlsafe_b64decode(data).decode('utf-8')
                        break
                elif part['mimeType'] == 'text/html':
                    data = part['body'].get('data')
                    if data:
                        html = base64.urlsafe_b64decode(data).decode('utf-8')
                        soup = BeautifulSoup(html, 'html.parser')
                        body = soup.get_text()
        else:
            if payload['mimeType'] == 'text/plain':
                data = payload['body'].get('data')
                if data:
                    body = base64.urlsafe_b64decode(data).decode('utf-8')
        
        return body.strip()
    
    def _extract_attachments(self, message_id: str, payload: Dict[str, Any]) -> List[EmailAttachment]:
        """Extract email attachments."""
        attachments = []
        
        def process_part(part):
            if part.get('filename'):
                attachment_id = part['body'].get('attachmentId')
                if attachment_id:
                    attachment = EmailAttachment(
                        filename=part['filename'],
                        content_type=part.get('mimeType'),
                        size_bytes=part['body'].get('size', 0)
                    )
                    
                    # Get attachment data if it's a PDF
                    if part['filename'].lower().endswith('.pdf'):
                        try:
                            att_data = self.service.users().messages().attachments().get(
                                userId='me',
                                messageId=message_id,
                                id=attachment_id
                            ).execute()
                            
                            attachment.data = base64.urlsafe_b64decode(att_data['data'])
                        except Exception as e:
                            logger.error(f"Failed to get attachment data: {e}")
                    
                    attachments.append(attachment)
        
        if 'parts' in payload:
            for part in payload['parts']:
                process_part(part)
                if 'parts' in part:
                    for subpart in part['parts']:
                        process_part(subpart)
        
        return attachments
    
    def _parse_date(self, date_str: Optional[str]) -> Optional[datetime]:
        """Parse email date string."""
        if not date_str:
            return None
        
        try:
            from email.utils import parsedate_to_datetime
            return parsedate_to_datetime(date_str)
        except Exception as e:
            logger.warning(f"Failed to parse date '{date_str}': {e}")
            return None
    
    def mark_as_read(self, message_id: str) -> bool:
        """Mark email as read."""
        try:
            self.service.users().messages().modify(
                userId='me',
                id=message_id,
                body={'removeLabelIds': ['UNREAD']}
            ).execute()
            return True
        except Exception as e:
            logger.error(f"Failed to mark email as read: {e}")
            return False
    
    def add_label(self, message_id: str, label_name: str) -> bool:
        """Add label to email."""
        try:
            # Get label ID
            labels = self.service.users().labels().list(userId='me').execute()
            label_id = None
            for label in labels.get('labels', []):
                if label['name'] == label_name:
                    label_id = label['id']
                    break
            
            if not label_id:
                logger.warning(f"Label '{label_name}' not found")
                return False
            
            self.service.users().messages().modify(
                userId='me',
                id=message_id,
                body={'addLabelIds': [label_id]}
            ).execute()
            return True
        except Exception as e:
            logger.error(f"Failed to add label: {e}")
            return False
