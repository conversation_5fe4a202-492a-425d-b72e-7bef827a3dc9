"""
Data validation utilities.
"""

import re
import logging
from typing import List, Dict, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>
from datetime import datetime

from core.models import EmailData, ExtractedOrder, OrderLine, CustomerDetails
from core.exceptions import ValidationError

logger = logging.getLogger(__name__)


def validate_email_data(email_data: EmailData) -> Tuple[bool, List[str]]:
    """
    Validate email data structure and content.
    
    Args:
        email_data: EmailData object to validate
        
    Returns:
        Tuple of (is_valid, list_of_errors)
    """
    errors = []
    
    # Required fields
    if not email_data.gmail_id:
        errors.append("Gmail ID is required")
    
    if not email_data.subject:
        errors.append("Subject is required")
    
    if not email_data.sender:
        errors.append("Sender is required")
    
    if not email_data.body:
        errors.append("Body is required")
    
    # Email format validation
    if email_data.sender and not _is_valid_email_format(email_data.sender):
        errors.append(f"Invalid sender email format: {email_data.sender}")
    
    if email_data.recipient and not _is_valid_email_format(email_data.recipient):
        errors.append(f"Invalid recipient email format: {email_data.recipient}")
    
    # Date validation
    if email_data.received_date and email_data.received_date > datetime.now():
        errors.append("Received date cannot be in the future")
    
    # Attachment validation
    if email_data.has_attachments and not email_data.attachments:
        errors.append("has_attachments is True but no attachments provided")
    
    if not email_data.has_attachments and email_data.attachments:
        errors.append("has_attachments is False but attachments provided")
    
    return len(errors) == 0, errors


def validate_order_data(order_data: ExtractedOrder) -> Tuple[bool, List[str]]:
    """
    Validate extracted order data.
    
    Args:
        order_data: ExtractedOrder object to validate
        
    Returns:
        Tuple of (is_valid, list_of_errors)
    """
    errors = []
    
    # Customer validation
    customer_valid, customer_errors = validate_customer_details(order_data.customer_details)
    errors.extend(customer_errors)
    
    # Order lines validation
    if not order_data.order_lines:
        errors.append("Order must have at least one line item")
    else:
        for i, line in enumerate(order_data.order_lines):
            line_valid, line_errors = validate_order_line(line)
            errors.extend([f"Line {i+1}: {error}" for error in line_errors])
    
    # PO number validation
    if order_data.purchase_order_number:
        if not _is_valid_po_number(order_data.purchase_order_number):
            errors.append(f"Invalid PO number format: {order_data.purchase_order_number}")
    
    # Amount validation
    if order_data.total_amount is not None:
        if order_data.total_amount < 0:
            errors.append("Total amount cannot be negative")
    
    # Currency validation
    if order_data.currency and not _is_valid_currency_code(order_data.currency):
        errors.append(f"Invalid currency code: {order_data.currency}")
    
    return len(errors) == 0, errors


def validate_customer_details(customer: CustomerDetails) -> Tuple[bool, List[str]]:
    """
    Validate customer details.
    
    Args:
        customer: CustomerDetails object to validate
        
    Returns:
        Tuple of (is_valid, list_of_errors)
    """
    errors = []
    
    # Customer name is required
    if not customer.customer_name or not customer.customer_name.strip():
        errors.append("Customer name is required")
    
    # Debtor ID validation
    if customer.debtor_id is not None:
        if customer.debtor_id <= 0:
            errors.append("Debtor ID must be positive")
    
    # Email validation
    if customer.email and not _is_valid_email_format(customer.email):
        errors.append(f"Invalid customer email format: {customer.email}")
    
    # Phone validation
    if customer.phone and not _is_valid_phone_format(customer.phone):
        errors.append(f"Invalid phone number format: {customer.phone}")
    
    return len(errors) == 0, errors


def validate_order_line(line: OrderLine) -> Tuple[bool, List[str]]:
    """
    Validate order line item.
    
    Args:
        line: OrderLine object to validate
        
    Returns:
        Tuple of (is_valid, list_of_errors)
    """
    errors = []
    
    # Stock code is required
    if not line.stock_code or not line.stock_code.strip():
        errors.append("Stock code is required")
    
    # Quantity validation
    if line.quantity <= 0:
        errors.append("Quantity must be greater than 0")
    
    # Price validation
    if line.unit_price is not None and line.unit_price < 0:
        errors.append("Unit price cannot be negative")
    
    if line.line_total is not None and line.line_total < 0:
        errors.append("Line total cannot be negative")
    
    # Unit of measure validation
    if line.unit_of_measure and not _is_valid_uom(line.unit_of_measure):
        errors.append(f"Invalid unit of measure: {line.unit_of_measure}")
    
    return len(errors) == 0, errors


def _is_valid_email_format(email: str) -> bool:
    """Check if email format is valid."""
    if not email:
        return False
    
    # <AUTHOR> <EMAIL>" format
    email_match = re.search(r'<([^>]+)>', email)
    if email_match:
        email = email_match.group(1)
    
    # Basic email regex
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def _is_valid_po_number(po_number: str) -> bool:
    """Check if PO number format is valid."""
    if not po_number:
        return False
    
    # Allow alphanumeric characters, hyphens, and underscores
    # Length between 3 and 50 characters
    pattern = r'^[A-Za-z0-9\-_]{3,50}$'
    return bool(re.match(pattern, po_number))


def _is_valid_currency_code(currency: str) -> bool:
    """Check if currency code is valid."""
    if not currency:
        return False
    
    # Common currency codes
    valid_currencies = {
        'AUD', 'USD', 'EUR', 'GBP', 'JPY', 'CAD', 'CHF', 'CNY', 'NZD'
    }
    
    return currency.upper() in valid_currencies


def _is_valid_phone_format(phone: str) -> bool:
    """Check if phone number format is valid."""
    if not phone:
        return False
    
    # Remove common formatting characters
    cleaned = re.sub(r'[\s\-\(\)\+]', '', phone)
    
    # Check if it's all digits and reasonable length
    return cleaned.isdigit() and 8 <= len(cleaned) <= 15


def _is_valid_uom(uom: str) -> bool:
    """Check if unit of measure is valid."""
    if not uom:
        return False
    
    # Common units of measure
    valid_uoms = {
        'EA', 'EACH', 'PC', 'PCS', 'PIECE', 'PIECES',
        'KG', 'G', 'LB', 'OZ',
        'L', 'ML', 'GAL', 'QT',
        'M', 'CM', 'MM', 'FT', 'IN',
        'BOX', 'CASE', 'PACK', 'SET',
        'HOUR', 'DAY', 'WEEK', 'MONTH'
    }
    
    return uom.upper() in valid_uoms


def validate_stock_code(stock_code: str) -> Tuple[bool, Optional[str]]:
    """
    Validate stock code format.
    
    Args:
        stock_code: Stock code to validate
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if not stock_code:
        return False, "Stock code is required"
    
    # Remove whitespace
    stock_code = stock_code.strip()
    
    if len(stock_code) < 2:
        return False, "Stock code must be at least 2 characters"
    
    if len(stock_code) > 50:
        return False, "Stock code must be 50 characters or less"
    
    # Allow alphanumeric characters, hyphens, and underscores
    if not re.match(r'^[A-Za-z0-9\-_]+$', stock_code):
        return False, "Stock code can only contain letters, numbers, hyphens, and underscores"
    
    return True, None


def sanitize_text_input(text: str, max_length: int = 1000) -> str:
    """
    Sanitize text input for safe processing.
    
    Args:
        text: Text to sanitize
        max_length: Maximum allowed length
        
    Returns:
        Sanitized text
    """
    if not text:
        return ""
    
    # Remove control characters except newlines and tabs
    text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
    
    # Normalize whitespace
    text = re.sub(r'\s+', ' ', text)
    
    # Trim to max length
    if len(text) > max_length:
        text = text[:max_length].rstrip() + "..."
    
    return text.strip()


def validate_date_range(start_date: Optional[datetime], 
                       end_date: Optional[datetime]) -> Tuple[bool, Optional[str]]:
    """
    Validate date range.
    
    Args:
        start_date: Start date
        end_date: End date
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if start_date and end_date:
        if start_date > end_date:
            return False, "Start date cannot be after end date"
        
        # Check for reasonable date range (not more than 10 years)
        if (end_date - start_date).days > 3650:
            return False, "Date range cannot exceed 10 years"
    
    # Check for future dates
    now = datetime.now()
    if start_date and start_date > now:
        return False, "Start date cannot be in the future"
    
    if end_date and end_date > now:
        return False, "End date cannot be in the future"
    
    return True, None
