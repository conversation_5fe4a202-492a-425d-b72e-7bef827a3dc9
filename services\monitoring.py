"""
Health monitoring service for system status.
"""

import logging
import time
import psutil
from typing import Dict, Any, List
from datetime import datetime

from core.models import HealthStatus
from core.config import config

logger = logging.getLogger(__name__)


class HealthMonitor:
    """System health monitoring service."""
    
    def __init__(self):
        """Initialize health monitor."""
        self.services = {}
        logger.info("Health monitor initialized")
    
    def check_all_services(self) -> Dict[str, HealthStatus]:
        """Check health of all services."""
        results = {}
        
        # Check Gmail service
        results['gmail'] = self._check_gmail_service()
        
        # Check MYOB service
        results['myob'] = self._check_myob_service()
        
        # Check database service
        results['database'] = self._check_database_service()
        
        # Check system resources
        results['system'] = self._check_system_resources()
        
        return results
    
    def _check_gmail_service(self) -> HealthStatus:
        """Check Gmail service health."""
        start_time = time.time()
        
        try:
            from services.email import GmailService
            
            # Try to initialize Gmail service
            gmail_service = GmailService()
            
            # Simple health check - try to access the service
            if gmail_service.service:
                response_time = (time.time() - start_time) * 1000
                return HealthStatus(
                    service_name="gmail",
                    status="healthy",
                    last_check=datetime.now(),
                    response_time_ms=response_time
                )
            else:
                return HealthStatus(
                    service_name="gmail",
                    status="unhealthy",
                    last_check=datetime.now(),
                    error_message="Gmail service not initialized"
                )
                
        except Exception as e:
            return HealthStatus(
                service_name="gmail",
                status="unhealthy",
                last_check=datetime.now(),
                error_message=str(e)
            )
    
    def _check_myob_service(self) -> HealthStatus:
        """Check MYOB service health."""
        start_time = time.time()
        
        try:
            from services.erp import MYOBService
            
            myob_service = MYOBService()
            
            # Try to validate connection
            if myob_service.validate_connection():
                response_time = (time.time() - start_time) * 1000
                return HealthStatus(
                    service_name="myob",
                    status="healthy",
                    last_check=datetime.now(),
                    response_time_ms=response_time
                )
            else:
                return HealthStatus(
                    service_name="myob",
                    status="degraded",
                    last_check=datetime.now(),
                    error_message="MYOB connection validation failed"
                )
                
        except Exception as e:
            return HealthStatus(
                service_name="myob",
                status="unhealthy",
                last_check=datetime.now(),
                error_message=str(e)
            )
    
    def _check_database_service(self) -> HealthStatus:
        """Check database service health."""
        start_time = time.time()
        
        try:
            from services.database import SupabaseService
            
            db_service = SupabaseService()
            
            # Try to test connection
            if db_service.test_connection():
                response_time = (time.time() - start_time) * 1000
                return HealthStatus(
                    service_name="database",
                    status="healthy",
                    last_check=datetime.now(),
                    response_time_ms=response_time
                )
            else:
                return HealthStatus(
                    service_name="database",
                    status="degraded",
                    last_check=datetime.now(),
                    error_message="Database connection test failed"
                )
                
        except Exception as e:
            return HealthStatus(
                service_name="database",
                status="unhealthy",
                last_check=datetime.now(),
                error_message=str(e)
            )
    
    def _check_system_resources(self) -> HealthStatus:
        """Check system resource health."""
        try:
            # Get system metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Determine status based on resource usage
            status = "healthy"
            warnings = []
            
            if cpu_percent > 80:
                status = "degraded"
                warnings.append(f"High CPU usage: {cpu_percent}%")
            
            if memory.percent > 85:
                status = "degraded"
                warnings.append(f"High memory usage: {memory.percent}%")
            
            if disk.percent > 90:
                status = "degraded"
                warnings.append(f"High disk usage: {disk.percent}%")
            
            details = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'disk_percent': disk.percent,
                'memory_available_gb': memory.available / (1024**3),
                'disk_free_gb': disk.free / (1024**3)
            }
            
            return HealthStatus(
                service_name="system",
                status=status,
                last_check=datetime.now(),
                error_message="; ".join(warnings) if warnings else None,
                details=details
            )
            
        except Exception as e:
            return HealthStatus(
                service_name="system",
                status="unhealthy",
                last_check=datetime.now(),
                error_message=str(e)
            )
    
    def get_overall_status(self) -> str:
        """Get overall system status."""
        service_statuses = self.check_all_services()
        
        # Count status types
        healthy_count = sum(1 for status in service_statuses.values() if status.status == "healthy")
        degraded_count = sum(1 for status in service_statuses.values() if status.status == "degraded")
        unhealthy_count = sum(1 for status in service_statuses.values() if status.status == "unhealthy")
        
        total_services = len(service_statuses)
        
        if unhealthy_count > 0:
            return "unhealthy"
        elif degraded_count > 0:
            return "degraded"
        elif healthy_count == total_services:
            return "healthy"
        else:
            return "unknown"
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get comprehensive health summary."""
        service_statuses = self.check_all_services()
        overall_status = self.get_overall_status()
        
        return {
            'overall_status': overall_status,
            'timestamp': datetime.now().isoformat(),
            'services': {name: status.dict() for name, status in service_statuses.items()},
            'summary': {
                'total_services': len(service_statuses),
                'healthy': sum(1 for s in service_statuses.values() if s.status == "healthy"),
                'degraded': sum(1 for s in service_statuses.values() if s.status == "degraded"),
                'unhealthy': sum(1 for s in service_statuses.values() if s.status == "unhealthy")
            }
        }
