"""
Email processor for handling email operations and workflows.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime

from core.config import config
from core.models import EmailData, ProcessedOrder
from core.exceptions import EmailProcessingError
from services.email import GmailService
from services.database import SupabaseService
from services.llm import LLMService
from utils.pdf import extract_text_from_pdf

logger = logging.getLogger(__name__)


class EmailProcessor:
    """Main email processing orchestrator."""
    
    def __init__(self):
        """Initialize email processor."""
        self.gmail_service = GmailService()
        self.db_service = SupabaseService()
        self.llm_service = LLMService()
        
        logger.info("Email processor initialized")
    
    def process_emails_by_labels(self, labels: Optional[List[str]] = None, 
                                max_emails_per_label: int = 20) -> Dict[str, Any]:
        """Process emails from specified labels."""
        if not labels:
            labels = config.GMAIL_LABELS_TO_PROCESS
        
        results = {
            'processed_count': 0,
            'error_count': 0,
            'labels_processed': [],
            'errors': []
        }
        
        for label in labels:
            try:
                logger.info(f"Processing emails from label: {label}")
                
                # Get emails from Gmail
                emails = self.gmail_service.get_emails_by_label(
                    label_name=label,
                    max_results=max_emails_per_label,
                    time_filter_hours=config.DEFAULT_EMAIL_TIME_FILTER_HOURS
                )
                
                label_results = self._process_email_batch(emails, label)
                
                results['processed_count'] += label_results['processed']
                results['error_count'] += label_results['errors']
                results['labels_processed'].append({
                    'label': label,
                    'emails_found': len(emails),
                    'processed': label_results['processed'],
                    'errors': label_results['errors']
                })
                
                logger.info(f"Completed processing label '{label}': {label_results['processed']} processed, {label_results['errors']} errors")
                
            except Exception as e:
                error_msg = f"Failed to process label '{label}': {e}"
                logger.error(error_msg)
                results['errors'].append(error_msg)
                results['error_count'] += 1
        
        return results
    
    def _process_email_batch(self, emails: List[EmailData], source_label: str) -> Dict[str, int]:
        """Process a batch of emails."""
        processed = 0
        errors = 0
        
        for email in emails:
            try:
                # Check if already processed
                existing = self.db_service.get_email_by_gmail_id(email.gmail_id)
                if existing:
                    logger.debug(f"Email already processed: {email.gmail_id}")
                    continue
                
                # Process the email
                result = self.process_single_email(email, source_label)
                
                if result:
                    processed += 1
                    # Mark as read in Gmail
                    self.gmail_service.mark_as_read(email.gmail_id)
                else:
                    errors += 1
                    
            except Exception as e:
                logger.error(f"Failed to process email {email.gmail_id}: {e}")
                errors += 1
        
        return {'processed': processed, 'errors': errors}
    
    def process_single_email(self, email_data: EmailData, source_label: str) -> Optional[ProcessedOrder]:
        """Process a single email completely."""
        try:
            logger.info(f"Processing email: {email_data.subject}")
            
            # Save email to database
            email_id = self.db_service.save_email(email_data)
            if not email_id:
                raise EmailProcessingError("Failed to save email to database")
            
            # Update processing status
            self.db_service.update_processing_status(email_id, "processing")
            
            # Extract PDF text if attachments exist
            pdf_text = None
            if email_data.attachments:
                pdf_text = self._extract_pdf_content(email_data.attachments)
            
            # Analyze email with LLM
            analysis = self.llm_service.analyze_email(email_data)
            
            # If it looks like an order, extract order data
            extracted_order = None
            if analysis.get('is_order', False) and analysis.get('confidence', 0) > 0.5:
                extracted_order = self.llm_service.extract_order_data(email_data, pdf_text)
            
            # Generate summary
            summary = self.llm_service.generate_summary(email_data, extracted_order)
            
            # Create processed order
            processed_order = ProcessedOrder(
                email_id=email_id,
                email_subject=email_data.subject,
                email_data=email_data,
                extracted_order=extracted_order,
                markdown_summary=summary,
                processing_status="completed" if extracted_order else "no_order_found"
            )
            
            # Save processed order
            order_id = self.db_service.save_processed_order(processed_order)
            
            # Update final status
            final_status = "completed" if extracted_order else "no_order_found"
            self.db_service.update_processing_status(email_id, final_status)
            
            logger.info(f"Successfully processed email: {email_data.subject}")
            return processed_order
            
        except Exception as e:
            error_msg = f"Email processing failed: {e}"
            logger.error(error_msg)
            
            # Update error status
            if 'email_id' in locals():
                self.db_service.update_processing_status(email_id, "error", str(e))
            
            raise EmailProcessingError(error_msg)
    
    def _extract_pdf_content(self, attachments: List) -> Optional[str]:
        """Extract text from PDF attachments."""
        pdf_texts = []
        
        for attachment in attachments:
            if attachment.filename.lower().endswith('.pdf') and attachment.data:
                try:
                    text = extract_text_from_pdf(attachment.data)
                    if text:
                        pdf_texts.append(text)
                        attachment.extracted_text = text
                        attachment.processed = True
                        logger.info(f"Extracted text from PDF: {attachment.filename}")
                except Exception as e:
                    logger.error(f"Failed to extract PDF text from {attachment.filename}: {e}")
        
        return "\n\n".join(pdf_texts) if pdf_texts else None
    
    def reprocess_failed_emails(self, limit: int = 50) -> Dict[str, Any]:
        """Reprocess emails that failed processing."""
        try:
            # Get emails with error status
            failed_emails = self.db_service.get_unprocessed_emails(limit)
            
            results = {
                'attempted': len(failed_emails),
                'successful': 0,
                'failed': 0,
                'errors': []
            }
            
            for email_record in failed_emails:
                try:
                    # Reconstruct EmailData from database record
                    email_data = self._reconstruct_email_data(email_record)
                    
                    # Reprocess
                    result = self.process_single_email(email_data, "reprocess")
                    
                    if result:
                        results['successful'] += 1
                    else:
                        results['failed'] += 1
                        
                except Exception as e:
                    error_msg = f"Reprocessing failed for email {email_record.get('id')}: {e}"
                    logger.error(error_msg)
                    results['errors'].append(error_msg)
                    results['failed'] += 1
            
            return results
            
        except Exception as e:
            logger.error(f"Reprocessing batch failed: {e}")
            raise EmailProcessingError(f"Reprocessing failed: {e}")
    
    def _reconstruct_email_data(self, email_record: Dict[str, Any]) -> EmailData:
        """Reconstruct EmailData from database record."""
        return EmailData(
            id=email_record.get('id'),
            gmail_id=email_record['gmail_id'],
            subject=email_record['subject'],
            sender=email_record['sender'],
            recipient=email_record.get('recipient'),
            body=email_record['body'],
            html_body=email_record.get('html_body'),
            thread_id=email_record.get('thread_id'),
            label_ids=email_record.get('label_ids', []),
            received_date=datetime.fromisoformat(email_record['received_date']) if email_record.get('received_date') else None,
            has_attachments=email_record.get('has_attachments', False),
            attachments=[]  # Attachments would need separate query
        )
    
    def get_processing_stats(self, days: int = 7) -> Dict[str, Any]:
        """Get email processing statistics."""
        return self.db_service.get_processing_stats(days)
