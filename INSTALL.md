# 🚀 Quick Installation Guide

## **Simple Installation**

```bash
# Clone the repository
git clone https://github.com/slothmc-ctrl/TeamsysV0.1.git
cd TeamsysV0.1

# Install everything you need
pip install -r requirements.txt
```

That's it! One command installs:
- ✅ Core dependencies (pydantic, google APIs, etc.)
- ✅ Enhanced features (pdfplumber, rich, httpx, etc.) 
- ✅ Development tools (pytest, black, ruff, mypy, etc.)

## **What You Get**

### **Core Features**
- Gmail API integration
- MYOB ERP integration  
- Supabase database
- Mistral AI LLM service
- PDF processing
- Email parsing

### **Enhanced Features**
- Advanced PDF extraction (pdfplumber)
- Rich terminal output
- Modern HTTP client (httpx)
- Retry logic (tenacity)
- Google Gemini AI

### **Development Tools**
- Testing framework (pytest)
- Code formatting (black)
- Modern linting (ruff)
- Type checking (mypy)
- Async testing support

## **Requirements**
- Python 3.10+ (3.12+ recommended)
- Your current Python 3.13.2 works perfectly!

## **Next Steps**
1. Configure your `.env` file
2. Run `python main.py --config-check`
3. Start using: `python main.py status`

## **Need Help?**
- Check `DEPENDENCIES.md` for detailed dependency info
- See `README.md` for full documentation
- All dependencies are July 2025 latest versions
