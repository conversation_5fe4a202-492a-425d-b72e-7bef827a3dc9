"""
Core module for TeamsysV0.1

Contains shared configuration, models, exceptions, and constants.
"""

from core.config import config
from core.models import (
    EmailData, EmailAttachment, ExtractedOrder, ProcessedOrder,
    MYOBPayload, CustomerDetails, OrderLine
)
from core.exceptions import (
    TeamsysException, ConfigurationError, EmailProcessingError,
    LLMServiceError, DatabaseError, MYOBError, ValidationError
)
from core.constants import (
    APP_NAME, APP_VERSION, DEFAULT_LABELS, MAX_EMAILS_PER_LABEL
)

__all__ = [
    # Configuration
    "config",
    # Models
    "EmailData", "EmailAttachment", "ExtractedOrder", "ProcessedOrder",
    "MYOBPayload", "CustomerDetails", "OrderLine",
    # Exceptions
    "TeamsysException", "ConfigurationError", "EmailProcessingError",
    "LLMServiceError", "DatabaseError", "MYOBError", "ValidationError",
    # Constants
    "APP_NAME", "APP_VERSION", "DEFAULT_LABELS", "MAX_EMAILS_PER_LABEL"
]
